using Microsoft.AspNetCore.Mvc;
using LicenseServer.Models;
using LicenseServer.Services;
using Microsoft.AspNetCore.Authorization;

namespace LicenseServer.Controllers
{
    [ApiController]
    [Route("api/license")]
    public class LicenseController : ControllerBase
    {
        private readonly LicenseReportService _licenseService;
        private readonly ILogger<LicenseController> _logger;
        private readonly IConfiguration _configuration;

        public LicenseController(
            LicenseReportService licenseService, 
            ILogger<LicenseController> logger,
            IConfiguration configuration)
        {
            _licenseService = licenseService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 接收客户端上报的授权状态
        /// </summary>
        [HttpPost("report")]
        public async Task<IActionResult> ReportLicense([FromBody] LicenseStatusReport report)
        {
            try
            {
                // 验证应用密钥
                if (!IsValidAppKey())
                {
                    _logger.LogWarning($"无效的API密钥，来自IP: {HttpContext.Connection.RemoteIpAddress}");
                    return Unauthorized(new { message = "无效的API密钥" });
                }

                _logger.LogInformation($"收到来自 {report.MachineName} ({report.MachineId}) 的授权状态报告");
                
                // 保存报告
                await _licenseService.SaveReportAsync(report);
                
                return Ok(new { status = "success", message = "授权状态已保存" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存授权状态报告时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取所有机器的最新授权状态
        /// </summary>
        [HttpGet("list")]
        [Authorize]
        public async Task<IActionResult> GetLicenseList()
        {
            try
            {
                var reports = await _licenseService.GetLatestReportsAsync();
                return Ok(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取授权状态列表时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取特定机器的授权状态历史
        /// </summary>
        [HttpGet("history/{machineId}")]
        [Authorize]
        public async Task<IActionResult> GetLicenseHistory(string machineId, [FromQuery] int limit = 50)
        {
            try
            {
                var reports = await _licenseService.GetReportHistoryAsync(machineId, limit);
                if (reports.Count == 0)
                {
                    return NotFound(new { status = "error", message = $"未找到机器ID为 {machineId} 的授权状态记录" });
                }
                
                return Ok(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取授权状态历史时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取特定机器的最新授权状态
        /// </summary>
        [HttpGet("status/{machineId}")]
        [Authorize]
        public async Task<IActionResult> GetLicenseStatus(string machineId)
        {
            try
            {
                var report = await _licenseService.GetLatestReportByMachineIdAsync(machineId);
                if (report == null)
                {
                    return NotFound(new { status = "error", message = $"未找到机器ID为 {machineId} 的授权状态记录" });
                }
                
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取授权状态时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取即将过期的授权
        /// </summary>
        [HttpGet("expiring")]
        [Authorize]
        public async Task<IActionResult> GetExpiringLicenses([FromQuery] int days = 30)
        {
            try
            {
                var reports = await _licenseService.GetExpiringLicensesAsync(days);
                return Ok(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取即将过期的授权时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取已过期的授权
        /// </summary>
        [HttpGet("expired")]
        [Authorize]
        public async Task<IActionResult> GetExpiredLicenses()
        {
            try
            {
                var reports = await _licenseService.GetExpiredLicensesAsync();
                return Ok(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取已过期的授权时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 清除所有授权数据（仅用于测试环境）
        /// </summary>
        [HttpDelete("clear-all")]
        [Authorize]
        public async Task<IActionResult> ClearAllData()
        {
            try
            {
                // 检查是否为开发环境
                var isDevelopment = _configuration.GetValue<bool>("IsDevelopmentEnvironment", false);
                
                if (!isDevelopment)
                {
                    _logger.LogWarning($"在非开发环境中尝试清除所有数据，操作被拒绝");
                    return BadRequest(new { status = "error", message = "此操作仅允许在开发环境中执行" });
                }
                
                _logger.LogWarning($"正在清除所有授权数据...");
                
                // 调用Service层方法清除数据
                int deletedCount = await _licenseService.ClearAllDataAsync();
                
                _logger.LogInformation($"已清除所有数据，共删除 {deletedCount} 条记录");
                
                return Ok(new { 
                    status = "success", 
                    message = "所有数据已清除",
                    deletedCount = deletedCount 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"清除数据时出错: {ex.Message}");
                return StatusCode(500, new { status = "error", message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 验证API密钥
        /// </summary>
        private bool IsValidAppKey()
        {
            // 从配置中获取预设的API密钥
            var configAppKey = _configuration["AppSettings:ApiKey"];
            
            // 从请求头中获取API密钥
            if (Request.Headers.TryGetValue("X-App-Key", out var appKey))
            {
                return appKey == configAppKey;
            }
            
            return false;
        }
    }
} 