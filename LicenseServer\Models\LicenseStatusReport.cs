using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LicenseServer.Models
{
    public class LicenseStatusReport
    {
        [Key]
        public int Id { get; set; }

        // 客户端标识信息
        public string MachineId { get; set; } = "";
        public string MachineName { get; set; } = "";
        public string IpAddress { get; set; } = "";
        public string WindowsVersion { get; set; } = "";
        public string ApplicationVersion { get; set; } = "";
        
        // 添加时区说明和格式化特性
        [System.ComponentModel.DataAnnotations.DataType(System.ComponentModel.DataAnnotations.DataType.DateTime)]
        [System.ComponentModel.DataAnnotations.DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm}")]
        public DateTime ReportTime { get; set; }
        
        // 授权状态
        public bool IsLicensed { get; set; }
        public string HardwareId { get; set; } = "";
        public string LicenseHardwareId { get; set; } = "";
        
        // 评估锁状态
        public bool EvaluationLockEnabled { get; set; }
        public string EvaluationType { get; set; } = "";
        public int EvaluationTime { get; set; }
        public int EvaluationTimeCurrent { get; set; }
        
        // 到期日期锁定状态
        public bool ExpirationDateLockEnabled { get; set; }
        public DateTime? ExpirationDate { get; set; }
        
        // 使用次数锁定状态
        public bool NumberOfUsesLockEnabled { get; set; }
        public int MaxUses { get; set; }
        public int CurrentUses { get; set; }
        
        // 实例数量锁定状态
        public bool NumberOfInstancesLockEnabled { get; set; }
        public int MaxInstances { get; set; }
        
        // 硬件锁状态
        public bool HardwareLockEnabled { get; set; }
        
        // 附加信息 - 存储为JSON - 使用MySQL兼容的类型
        [Column(TypeName = "longtext")]
        public string AdditionalInfoJson { get; set; } = "{}";
        
        // 非数据库字段，转换为Dictionary
        [NotMapped]
        public Dictionary<string, string> AdditionalInfo { get; set; } = new Dictionary<string, string>();
    }
} 