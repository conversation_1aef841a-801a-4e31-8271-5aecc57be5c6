{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "UseInMemoryDatabase": false, "UseHttpsRedirection": false, "IsDevelopmentEnvironment": true, "DatabaseOptions": {"ResetOnStartup": false, "ApplyMigrations": true, "EnableDetailedLogging": true}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=InduvisionLicenses;User=root;Password=*********;", "InMemoryConnection": "InMemoryDatabase"}, "JwtSettings": {"Secret": "YourSecretKeyHere_AtLeast32Characters_ForHS256", "ExpiryHours": 24}, "AppSettings": {"ApiKey": "InduvisionDefaultKey2024"}, "LicenseSettings": {"DefaultProjectPath": "Config\\InduVisionV2.nrproj", "LicensesOutputPath": "licenses"}}