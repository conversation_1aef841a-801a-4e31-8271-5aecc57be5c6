<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InduVision 授权管理系统</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #212529;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 5px;
            margin: 5px 0;
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .status-card {
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .bg-license-valid {
            background-color: #d1e7dd;
        }
        .bg-license-expiring {
            background-color: #fff3cd;
        }
        .bg-license-expired {
            background-color: #f8d7da;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        #license-list .table {
            white-space: nowrap;
        }
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 3rem;
            height: 3rem;
        }
        /* 添加列宽控制样式 */
        .table .col-device-name {
            width: 15%;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .table .col-hardware-id {
            width: 20%;
            min-width: 180px;
        }
        .table .col-ip {
            width: 15%;
        }
        .table .col-status {
            width: 12%;
        }
        .table .col-expire {
            width: 15%;
        }
        .table .col-report-time {
            width: 15%;
        }
        .table .col-actions {
            width: 8%;
        }
        /* 增加表格响应式处理 */
        .table-responsive {
            overflow-x: auto;
        }
        /* 确保硬件ID不会被截断 */
        .hardware-id {
            word-break: break-all;
            white-space: normal;
        }
    </style>
</head>
<body>
    <div class="loading" id="loading">
        <div class="spinner-border loading-spinner text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 p-0">
                <div class="sidebar p-3">
                    <div class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white">
                        <i class="bi bi-shield-check fs-4 me-2"></i>
                        <span class="fs-4">授权管理系统</span>
                    </div>
                    <hr class="text-white">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="#dashboard" class="sidebar-link active" data-page="dashboard">
                                <i class="bi bi-speedometer2"></i>
                                控制面板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#licenses" class="sidebar-link" data-page="licenses">
                                <i class="bi bi-key"></i>
                                授权列表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#expiring" class="sidebar-link" data-page="expiring">
                                <i class="bi bi-hourglass-split"></i>
                                即将到期
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#expired" class="sidebar-link" data-page="expired">
                                <i class="bi bi-x-circle"></i>
                                已过期
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#license-gen" class="sidebar-link" data-page="license-gen">
                                <i class="bi bi-file-earmark-plus"></i>
                                授权生成
                            </a>
                        </li>
                    </ul>
                    <hr class="text-white">
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-2"></i>
                            <strong>管理员</strong>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                            <li><a class="dropdown-item" href="#" id="clear-data-btn">清除所有数据</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 content">
                <div id="page-dashboard" class="page-content">
                    <h2 class="mb-4"><i class="bi bi-speedometer2"></i> 控制面板</h2>
                    
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">总授权数</h5>
                                    <h2 class="card-text" id="total-licenses">-</h2>
                                    <p class="card-text">活跃客户端</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">有效授权</h5>
                                    <h2 class="card-text" id="valid-licenses">-</h2>
                                    <p class="card-text">正常工作中</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body">
                                    <h5 class="card-title">即将到期</h5>
                                    <h2 class="card-text" id="expiring-licenses">-</h2>
                                    <p class="card-text">30天内到期</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5 class="card-title">已过期</h5>
                                    <h2 class="card-text" id="expired-licenses">-</h2>
                                    <p class="card-text">需要续期</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>最近上报状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="col-device-name">设备名称</th>
                                            <th class="col-hardware-id">硬件ID</th>
                                            <th class="col-status">授权状态</th>
                                            <th class="col-expire">到期日期</th>
                                            <th class="col-report-time">上报时间</th>
                                            <th class="col-actions">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-reports">
                                        <tr>
                                            <td colspan="6" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="page-licenses" class="page-content d-none">
                    <h2 class="mb-4"><i class="bi bi-key"></i> 授权列表</h2>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>所有授权</h5>
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="license-search" placeholder="搜索机器名称或硬件ID...">
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="col-device-name">设备名称</th>
                                            <th class="col-hardware-id">硬件ID</th>
                                            <th class="col-ip">IP地址</th>
                                            <th class="col-status">授权状态</th>
                                            <th class="col-expire">到期日期</th>
                                            <th class="col-report-time">上报时间</th>
                                            <th class="col-actions">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="license-list">
                                        <tr>
                                            <td colspan="7" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="page-expiring" class="page-content d-none">
                    <h2 class="mb-4"><i class="bi bi-hourglass-split"></i> 即将到期</h2>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>30天内到期的授权</h5>
                                </div>
                                <div class="col-md-6 text-end">
                                    <select class="form-select" id="expiring-days">
                                        <option value="7">7天内</option>
                                        <option value="15">15天内</option>
                                        <option value="30" selected>30天内</option>
                                        <option value="60">60天内</option>
                                        <option value="90">90天内</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="col-device-name">设备名称</th>
                                            <th class="col-hardware-id">硬件ID</th>
                                            <th class="col-ip">IP地址</th>
                                            <th class="col-expire">到期日期</th>
                                            <th class="col-actions">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="expiring-list">
                                        <tr>
                                            <td colspan="5" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="page-expired" class="page-content d-none">
                    <h2 class="mb-4"><i class="bi bi-x-circle"></i> 已过期</h2>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>已过期授权</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="col-device-name">设备名称</th>
                                            <th class="col-hardware-id">硬件ID</th>
                                            <th class="col-ip">IP地址</th>
                                            <th class="col-expire">过期日期</th>
                                            <th class="col-actions">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="expired-list">
                                        <tr>
                                            <td colspan="5" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="page-license-gen" class="page-content d-none">
                    <h2 class="mb-4"><i class="bi bi-file-earmark-plus"></i> 授权生成</h2>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>生成新授权</h5>
                                </div>
                                <div class="card-body">
                                    <form id="license-gen-form">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="project-file" class="form-label">项目文件</label>
                                                <select class="form-select" id="project-file" required>
                                                    <option value="">选择项目文件...</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="hardware-id" class="form-label">硬件ID</label>
                                                <input type="text" class="form-control" id="hardware-id" placeholder="留空使用任意硬件">
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="expiration-date" class="form-label">到期日期</label>
                                                <input type="date" class="form-control" id="expiration-date">
                                                <div class="mt-2">
                                                    <small class="text-muted">快捷设置：</small>
                                                    <div class="btn-group btn-group-sm mt-1" role="group">
                                                        <button type="button" class="btn btn-outline-secondary" onclick="setExpirationDate(3)">3个月</button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="setExpirationDate(6)">6个月</button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="setExpirationDate(12)">1年</button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="setExpirationDate(24)">2年</button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="setExpirationDate(100)">100年</button>
                                                        <button type="button" class="btn btn-outline-warning" onclick="setExpirationDate(0)">永久</button>
                                                    </div>
                                                </div>
                                                <div class="form-check mt-2">
                                                    <input class="form-check-input" type="checkbox" id="expiration-lock-enabled">
                                                    <label class="form-check-label" for="expiration-lock-enabled">
                                                        启用过期锁
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="evaluation-time" class="form-label">评估时间</label>
                                                <input type="number" class="form-control" id="evaluation-time" placeholder="评估天数" min="1">
                                                <div class="form-check mt-2">
                                                    <input class="form-check-input" type="checkbox" id="evaluation-lock-enabled">
                                                    <label class="form-check-label" for="evaluation-lock-enabled">
                                                        启用评估锁
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="max-uses" class="form-label">最大使用次数</label>
                                                <input type="number" class="form-control" id="max-uses" placeholder="留空不限制" min="1">
                                                <div class="form-check mt-2">
                                                    <input class="form-check-input" type="checkbox" id="uses-lock-enabled">
                                                    <label class="form-check-label" for="uses-lock-enabled">
                                                        启用使用次数锁
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="max-instances" class="form-label">最大实例数</label>
                                                <input type="number" class="form-control" id="max-instances" placeholder="留空不限制" min="1">
                                                <div class="form-check mt-2">
                                                    <input class="form-check-input" type="checkbox" id="instances-lock-enabled">
                                                    <label class="form-check-label" for="instances-lock-enabled">
                                                        启用实例数锁
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="hardware-lock-enabled" checked>
                                                    <label class="form-check-label" for="hardware-lock-enabled">
                                                        启用硬件锁
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="output-filename" class="form-label">输出文件名</label>
                                                <input type="text" class="form-control" id="output-filename" placeholder="留空使用默认名称">
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">附加信息</label>
                                            <div id="additional-info-container">
                                                <div class="row additional-info-row mb-2">
                                                    <div class="col-md-5">
                                                        <input type="text" class="form-control additional-key" placeholder="键">
                                                    </div>
                                                    <div class="col-md-5">
                                                        <input type="text" class="form-control additional-value" placeholder="值">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-outline-danger btn-sm remove-info-btn">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="add-info-btn">
                                                <i class="bi bi-plus"></i> 添加附加信息
                                            </button>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <button type="submit" class="btn btn-primary w-100">
                                                    <i class="bi bi-download"></i> 生成并下载
                                                </button>
                                            </div>
                                            <div class="col-md-6">
                                                <button type="button" class="btn btn-success w-100" id="save-license-btn">
                                                    <i class="bi bi-save"></i> 生成并保存到服务器
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>系统状态</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">LicenseGen.dll 状态</label>
                                        <div class="alert alert-info" id="dll-status">
                                            <i class="bi bi-hourglass-split"></i> 检查中...
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">快速操作</label>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-primary btn-sm" id="test-project-btn">
                                                <i class="bi bi-play-circle"></i> 测试项目文件
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" id="refresh-projects-btn">
                                                <i class="bi bi-arrow-clockwise"></i> 刷新项目文件
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" id="clear-form-btn">
                                                <i class="bi bi-arrow-counterclockwise"></i> 清空表单
                                            </button>
                                            <button class="btn btn-outline-warning btn-sm" id="debug-api-btn" onclick="testProjectsApi()">
                                                <i class="bi bi-bug"></i> 调试API
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5>已生成的授权</h5>
                                </div>
                                <div class="card-body">
                                    <div id="generated-licenses-list">
                                        <div class="text-center text-muted">
                                            <i class="bi bi-folder2-open fs-1"></i>
                                            <p>暂无已生成的授权文件</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="licenseDetailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">授权详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <hr>
                            <p><strong>设备名称:</strong> <span id="detail-machine-name"></span></p>
                            <p><strong>硬件ID:</strong> <span id="detail-hardware-id"></span></p>
                            <p><strong>IP地址:</strong> <span id="detail-ip-address"></span></p>
                            <p><strong>Windows版本:</strong> <span id="detail-windows-version"></span></p>
                            <p><strong>应用版本:</strong> <span id="detail-app-version"></span></p>
                            <p><strong>上报时间:</strong> <span id="detail-report-time"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>授权状态</h6>
                            <hr>
                            <p><strong>是否有效:</strong> <span id="detail-is-licensed"></span></p>
                            <p><strong>硬件ID匹配:</strong> <span id="detail-hardware-match"></span></p>
                            <p><strong>到期日期:</strong> <span id="detail-expiration-date"></span></p>
                            <p><strong>评估类型:</strong> <span id="detail-evaluation-type"></span></p>
                            <p><strong>已使用次数:</strong> <span id="detail-current-uses"></span> / <span id="detail-max-uses"></span></p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>锁定状态</h6>
                            <hr>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card text-center p-2 mb-2">
                                        <p><i class="bi bi-clock-history fs-3"></i></p>
                                        <h6>评估锁</h6>
                                        <p id="detail-evaluation-lock" class="mb-0">-</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center p-2 mb-2">
                                        <p><i class="bi bi-calendar-x fs-3"></i></p>
                                        <h6>过期锁</h6>
                                        <p id="detail-expiration-lock" class="mb-0">-</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center p-2 mb-2">
                                        <p><i class="bi bi-123 fs-3"></i></p>
                                        <h6>使用次数锁</h6>
                                        <p id="detail-uses-lock" class="mb-0">-</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center p-2 mb-2">
                                        <p><i class="bi bi-pc-display fs-3"></i></p>
                                        <h6>硬件锁</h6>
                                        <p id="detail-hardware-lock" class="mb-0">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>附加信息</h6>
                            <hr>
                            <div id="detail-additional-info-container">
                                <table class="table table-bordered" id="detail-additional-info">
                                    <tbody>
                                        <tr>
                                            <td colspan="2">无附加信息</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1" data-bs-backdrop="static" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">登录授权管理系统</h5>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger d-none" id="login-error">
                        用户名或密码错误
                    </div>
                    <form id="login-form">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="login-btn">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 清除数据确认对话框 -->
    <div class="modal fade" id="clear-data-modal" tabindex="-1" aria-labelledby="clearDataModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="clearDataModalLabel">清除所有数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill"></i> 警告：此操作将<strong>永久删除</strong>所有授权状态数据且不可恢复！
                    </div>
                    <p>请输入<strong>CONFIRM</strong>以确认操作：</p>
                    <input type="text" class="form-control" id="clear-data-confirm" placeholder="输入CONFIRM以确认">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-secondary" id="confirm-clear-data-btn" disabled>删除所有数据</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html> 