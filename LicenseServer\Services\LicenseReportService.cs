using LicenseServer.Data;
using LicenseServer.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace LicenseServer.Services
{
    public class LicenseReportService
    {
        private readonly LicenseDbContext _context;
        private readonly ILogger<LicenseReportService> _logger;

        public LicenseReportService(LicenseDbContext context, ILogger<LicenseReportService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 保存授权状态报告
        /// </summary>
        public async Task SaveReportAsync(LicenseStatusReport report)
        {
            try
            {
                // 确保ReportTime是UTC时间
                if (report.ReportTime.Kind != DateTimeKind.Utc)
                {
                    // 如果时间不是UTC，则转换为UTC时间
                    report.ReportTime = DateTime.SpecifyKind(report.ReportTime, DateTimeKind.Utc);
                }

                // 将AdditionalInfo字典转换为JSON字符串
                if (report.AdditionalInfo != null && report.AdditionalInfo.Count > 0)
                {
                    report.AdditionalInfoJson = JsonSerializer.Serialize(report.AdditionalInfo);
                }

                // 添加到数据库
                await _context.LicenseReports.AddAsync(report);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"保存了来自 {report.MachineName} ({report.MachineId}) 的授权状态报告");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存授权状态报告时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取所有机器的最新授权状态
        /// </summary>
        public async Task<List<LicenseStatusReport>> GetLatestReportsAsync()
        {
            try
            {
                // 兼容旧版MySQL的查询方式
                // 先获取所有不同的机器ID
                var machineIds = await _context.LicenseReports
                    .Select(r => r.MachineId)
                    .Distinct()
                    .ToListAsync();

                // 为每个机器ID获取最新的报告
                List<LicenseStatusReport> latestReports = new List<LicenseStatusReport>();
                foreach (var machineId in machineIds)
                {
                    var report = await _context.LicenseReports
                        .Where(r => r.MachineId == machineId)
                        .OrderByDescending(r => r.ReportTime)
                        .FirstOrDefaultAsync();
                    
                    if (report != null)
                    {
                        latestReports.Add(report);
                    }
                }

                // 解析AdditionalInfoJson
                foreach (var report in latestReports)
                {
                    if (!string.IsNullOrEmpty(report.AdditionalInfoJson))
                    {
                        try
                        {
                            report.AdditionalInfo = JsonSerializer.Deserialize<Dictionary<string, string>>(report.AdditionalInfoJson)
                                ?? new Dictionary<string, string>();
                        }
                        catch
                        {
                            report.AdditionalInfo = new Dictionary<string, string>();
                        }
                    }
                }

                return latestReports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取最新授权状态报告时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取特定机器的授权状态历史
        /// </summary>
        public async Task<List<LicenseStatusReport>> GetReportHistoryAsync(string machineId, int limit = 50)
        {
            try
            {
                var reports = await _context.LicenseReports
                    .Where(r => r.MachineId == machineId)
                    .OrderByDescending(r => r.ReportTime)
                    .Take(limit)
                    .ToListAsync();

                // 解析AdditionalInfoJson
                foreach (var report in reports)
                {
                    if (!string.IsNullOrEmpty(report.AdditionalInfoJson))
                    {
                        try
                        {
                            report.AdditionalInfo = JsonSerializer.Deserialize<Dictionary<string, string>>(report.AdditionalInfoJson)
                                ?? new Dictionary<string, string>();
                        }
                        catch
                        {
                            report.AdditionalInfo = new Dictionary<string, string>();
                        }
                    }
                }

                return reports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取授权状态历史时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取特定机器的最新授权状态
        /// </summary>
        public async Task<LicenseStatusReport?> GetLatestReportByMachineIdAsync(string machineId)
        {
            try
            {
                var report = await _context.LicenseReports
                    .Where(r => r.MachineId == machineId)
                    .OrderByDescending(r => r.ReportTime)
                    .FirstOrDefaultAsync();

                if (report != null && !string.IsNullOrEmpty(report.AdditionalInfoJson))
                {
                    try
                    {
                        report.AdditionalInfo = JsonSerializer.Deserialize<Dictionary<string, string>>(report.AdditionalInfoJson)
                            ?? new Dictionary<string, string>();
                    }
                    catch
                    {
                        report.AdditionalInfo = new Dictionary<string, string>();
                    }
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取特定机器的最新授权状态时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取授权即将过期的报告
        /// </summary>
        public async Task<List<LicenseStatusReport>> GetExpiringLicensesAsync(int daysThreshold = 30)
        {
            try
            {
                // 计算阈值日期
                var thresholdDate = DateTime.UtcNow.AddDays(daysThreshold);

                // 首先获取每台机器的最新报告
                var latestReports = await GetLatestReportsAsync();

                // 然后筛选出即将过期的报告
                return latestReports
                    .Where(r => r.ExpirationDateLockEnabled && 
                               r.ExpirationDate.HasValue && 
                               r.ExpirationDate.Value <= thresholdDate &&
                               r.ExpirationDate.Value > DateTime.UtcNow)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取即将过期的授权时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取已过期的授权报告
        /// </summary>
        public async Task<List<LicenseStatusReport>> GetExpiredLicensesAsync()
        {
            try
            {
                // 首先获取每台机器的最新报告
                var latestReports = await GetLatestReportsAsync();

                // 然后筛选出已过期的报告
                return latestReports
                    .Where(r => (r.ExpirationDateLockEnabled && 
                                r.ExpirationDate.HasValue && 
                                r.ExpirationDate.Value < DateTime.UtcNow) ||
                                !r.IsLicensed)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取已过期的授权时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 清除所有授权数据
        /// </summary>
        /// <returns>删除的记录数量</returns>
        public async Task<int> ClearAllDataAsync()
        {
            try
            {
                _logger.LogWarning("正在执行清除所有授权数据的操作...");
                
                // 获取数据库中的记录总数
                int recordCount = await _context.LicenseReports.CountAsync();
                
                // 执行删除操作
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM licensereports");
                
                // 重置自增ID（MySQL特有语法）
                await _context.Database.ExecuteSqlRawAsync("ALTER TABLE licensereports AUTO_INCREMENT = 1");
                
                _logger.LogInformation($"已删除所有授权数据记录，共 {recordCount} 条");
                
                return recordCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"清除授权数据时出错: {ex.Message}");
                throw;
            }
        }
    }
} 