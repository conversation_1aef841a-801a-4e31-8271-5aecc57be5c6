<Reactor_Project ProjectFormat="2">
  <EnforceRelativePaths>false</EnforceRelativePaths>
  <Main_Assembly>D:\VSRepos\workstation2\bin\Release\net8.0-windows\InduVision.dll</Main_Assembly>
  <Main_Assembly_EnforceRelativePath>false</Main_Assembly_EnforceRelativePath>
  <General_Settings>
    <Automatic_Exception_Handling>true</Automatic_Exception_Handling>
    <Application_Compression>true</Application_Compression>
    <Show_Loading_Screen>false</Show_Loading_Screen>
    <Digital_Certificate_PFX_SPC_File />
    <Digital_Certificate_PVK_File />
    <Digital_Certificate_PFX_PVK_Password />
    <CodeSigningFriendlyName />
    <Time_Stamp_URL_SHA1>http://timestamp.digicert.com;http://timestamp.globalsign.com/scripts/timstamp.dll;http://timestamp.comodoca.com/authenticode;http://tsa.starfieldtech.com</Time_Stamp_URL_SHA1>
    <Time_Stamp_URL_SHA256>http://timestamp.digicert.com;http://timestamp.globalsign.com/?signature=sha2;http://sha256timestamp.ws.symantec.com/sha256/timestamp;http://timestamp.comodoca.com?td=sha256</Time_Stamp_URL_SHA256>
    <Embed_Assemblies>false</Embed_Assemblies>
    <Enable_Visual_Styles>true</Enable_Visual_Styles>
    <Force_Admin_Privileges>false</Force_Admin_Privileges>
    <Merge_Assemblies>false</Merge_Assemblies>
    <Enforce_Signing>false</Enforce_Signing>
    <Merge_Assembly_Attributes>true</Merge_Assembly_Attributes>
    <Embed_Assemblies_Strict_Version_Handling>false</Embed_Assemblies_Strict_Version_Handling>
    <Anti_Debug>true</Anti_Debug>
    <Hide_Method_Calls>true</Hide_Method_Calls>
    <General_Hide_Method_Calls_Externals>true</General_Hide_Method_Calls_Externals>
    <General_Hide_Method_Calls_Internals>false</General_Hide_Method_Calls_Internals>
    <Project_Targets_Mono_Framework>false</Project_Targets_Mono_Framework>
    <Strong_Name_Container />
    <Strong_Name_KeyPair_File />
    <Strong_Name_KeyPair_Password />
    <Search_Directories />
    <Seed>0</Seed>
    <Target_File>&lt;AssemblyLocation&gt;\&lt;AssemblyName&gt;_Secure\&lt;AssemblyFileName&gt;</Target_File>
    <V3_Mode>true</V3_Mode>
  </General_Settings>
  <Protection_Settings>
    <Anti_ILDASM>true</Anti_ILDASM>
    <Inject_Invalid_Metadata>true</Inject_Invalid_Metadata>
    <Anti_Tampering>true</Anti_Tampering>
    <Control_Flow_Obfuscation>true</Control_Flow_Obfuscation>
    <Control_Flow_Obfuscation_Method_Splitting>true</Control_Flow_Obfuscation_Method_Splitting>
    <Control_Flow_Obfuscation_Level>9</Control_Flow_Obfuscation_Level>
    <NamingConvention>Standard</NamingConvention>
    <CompressionMethod>Normal</CompressionMethod>
    <Native_EXE_File>false</Native_EXE_File>
    <Native_EXE_Embed_Pdb>false</Native_EXE_Embed_Pdb>
    <General_CoreBundle>false</General_CoreBundle>
    <General_CoreBundlePDB>false</General_CoreBundlePDB>
    <General_CopyPDB>true</General_CopyPDB>
    <General_CoreBundleCompress>false</General_CoreBundleCompress>
    <NecroBit>true</NecroBit>
    <NecroBit_Reflection_Compatibility_Mode>false</NecroBit_Reflection_Compatibility_Mode>
    <Obfuscation>true</Obfuscation>
    <Pruning>false</Pruning>
    <Create_Mapping_File>false</Create_Mapping_File>
    <Create_Mapping_File_Overwrite>false</Create_Mapping_File_Overwrite>
    <Merge_Mapping_Files>false</Merge_Mapping_Files>
    <Mapping_FileName>&lt;ProtectedAssemblyLocation&gt;\&lt;AssemblyName&gt;_&lt;AssemblyVersion&gt;.nrmap</Mapping_FileName>
    <Merge_XML_Doc>true</Merge_XML_Doc>
    <General_BundleName>&lt;AssemblyLocation&gt;\bundled\&lt;AssemblyName&gt;.exe</General_BundleName>
    <Exclude_Enums>false</Exclude_Enums>
    <Exclude_EnumFields>false</Exclude_EnumFields>
    <Exclude_Events>false</Exclude_Events>
    <Exclude_Fields>false</Exclude_Fields>
    <Exclude_Method_Parameters>false</Exclude_Method_Parameters>
    <Exclude_Application_Resources>true</Exclude_Application_Resources>
    <Exclude_Application_Settings>true</Exclude_Application_Settings>
    <Exclude_Namespaces>false</Exclude_Namespaces>
    <Exclude_Methods>false</Exclude_Methods>
    <Exclude_Properties>false</Exclude_Properties>
    <Exclude_Serializable_Types>false</Exclude_Serializable_Types>
    <Exclude_Types>false</Exclude_Types>
    <Include_Compiler_Serializable_Types>false</Include_Compiler_Serializable_Types>
    <Exclude_Compiler_Types>true</Exclude_Compiler_Types>
    <Exclude_Compiler_Properties>true</Exclude_Compiler_Properties>
    <Exclude_Compiler_Fields>false</Exclude_Compiler_Fields>
    <Exclude_Compiler_Parameters>true</Exclude_Compiler_Parameters>
    <Stealth_Obfuscation>false</Stealth_Obfuscation>
    <Ignore_InternalsVisibleTo>false</Ignore_InternalsVisibleTo>
    <DeclarativeProtection>false</DeclarativeProtection>
    <Incremental_Obfuscation>false</Incremental_Obfuscation>
    <Merge_Namespaces>false</Merge_Namespaces>
    <Merge_Namespaces_Namespace />
    <Merge_Enums>true</Merge_Enums>
    <Remove_Enum_Fields>true</Remove_Enum_Fields>
    <Obfuscate_All_Method_Parameters>false</Obfuscate_All_Method_Parameters>
    <Obfuscate_Public_Types>false</Obfuscate_Public_Types>
    <Use_Unprintable_Characters>false</Use_Unprintable_Characters>
    <Pre-JIT_Methods>false</Pre-JIT_Methods>
    <Public_Types_Internalization>false</Public_Types_Internalization>
    <Public_Types_Internalization_Honor_VisibilityRules>true</Public_Types_Internalization_Honor_VisibilityRules>
    <Virtualization>false</Virtualization>
    <Public_Types_Internalization_Exclusions>[internal]*</Public_Types_Internalization_Exclusions>
    <Resource_Encryption_And_Compression>true</Resource_Encryption_And_Compression>
    <Resource_Encryption_And_Compression_EmbeddedAssemblies>true</Resource_Encryption_And_Compression_EmbeddedAssemblies>
    <String_Encryption>true</String_Encryption>
    <String_Encryption_Mode>All</String_Encryption_Mode>
    <String_Encryption_InclusionExclusion_List />
    <Resource_Encryption_Exclusion_List />
    <Strong_Name_Removal_Protection>false</Strong_Name_Removal_Protection>
    <Rules>
      <Honor_Rules>false</Honor_Rules>
    </Rules>
  </Protection_Settings>
  <Lock_Settings>
    <Add_Licensing>true</Add_Licensing>
    <Expire_When_All_Expired>false</Expire_When_All_Expired>
    <Inbuilt_Lock_Evaluation_Period_Enabled>false</Inbuilt_Lock_Evaluation_Period_Enabled>
    <Inbuilt_Lock_Evaluation_Period_Type>Trial_Days</Inbuilt_Lock_Evaluation_Period_Type>
    <Inbuilt_Lock_Evaluation_Period_Time>14</Inbuilt_Lock_Evaluation_Period_Time>
    <Inbuilt_Lock_Expiry_Date_Enabled>false</Inbuilt_Lock_Expiry_Date_Enabled>
    <Inbuilt_Lock_Expiry_Date>20250805</Inbuilt_Lock_Expiry_Date>
    <Inbuilt_Lock_Number_Of_Uses_Enabled>false</Inbuilt_Lock_Number_Of_Uses_Enabled>
    <Inbuilt_Lock_Number_Of_Uses>10</Inbuilt_Lock_Number_Of_Uses>
    <Inbuilt_Lock_Max_Number_Of_Instances_Enabled>false</Inbuilt_Lock_Max_Number_Of_Instances_Enabled>
    <Inbuilt_Lock_Max_Number_Of_Instances>5</Inbuilt_Lock_Max_Number_Of_Instances>
    <Run_Another_Process_After_Expiration />
    <Run_Without_License_File>true</Run_Without_License_File>
    <Shutdown_Process_After_Expiration>false</Shutdown_Process_After_Expiration>
    <Lock_License_Fallback>false</Lock_License_Fallback>
    <Static_License_FileName />
  </Lock_Settings>
  <Dialog_Settings>
    <Dialog_Evaluation_Period_Enabled>true</Dialog_Evaluation_Period_Enabled>
    <Dialog_Evaluation_Period>尊敬的用户，欢迎试用本软件！您当前处于第 [current_minutes_days] 天（共 [max_minutes_days] 天试用期）。如需继续使用，请联系我们获取正式授权。</Dialog_Evaluation_Period>
    <Dialog_Expiry_Date_Enabled>true</Dialog_Expiry_Date_Enabled>
    <Dialog_Expiry_Date>Nag Screen! This message will disappear when a valid license file is installed. You are on day [current_minutes_days] of your [max_minutes_days] day evaluation period. You have [minutes_days_left] days left. You have used this software [current_uses] times out of a maximum of [max_uses]. You have [uses_left] uses left.</Dialog_Expiry_Date>
    <Dialog_Number_Of_Uses_Enabled>true</Dialog_Number_Of_Uses_Enabled>
    <Dialog_Number_Of_Uses>You have used this software [current_uses] times out of a maximum of [max_uses]. You have [uses_left] uses left. Your trial period is expired! You need to purchase a license to run this software.</Dialog_Number_Of_Uses>
    <Dialog_Max_Number_Of_Instances_Enabled>false</Dialog_Max_Number_Of_Instances_Enabled>
    <Dialog_Max_Number_Of_Instances>You can only run maximal [max_processes] instances of this software at the same time.</Dialog_Max_Number_Of_Instances>
    <Dialog_License_Not_Found_Enabled>false</Dialog_License_Not_Found_Enabled>
    <Dialog_License_Not_Found>如果没有有效的许可证文件，本软件将无法运行。要么找不到有效的许可证文件，要么许可证文件已过期。</Dialog_License_Not_Found>
    <Dialog_Nag_Screen_Enabled>false</Dialog_Nag_Screen_Enabled>
    <Dialog_Nag_Screen>安装有效的许可证文件后，此消息将消失。您当前处于第 [current_minutes_days] 天（共 [max_minutes_days] 天试用期），还剩 [minutes_days_left] 天。</Dialog_Nag_Screen>
    <Dialog_Nag_Screen_XDays>-1</Dialog_Nag_Screen_XDays>
    <Dialog_Box_Interface />
    <Dialog_Caption>锁定系统</Dialog_Caption>
    <Color_Gradient_Begin>255255255</Color_Gradient_Begin>
    <Color_Gradient_End>157211252</Color_Gradient_End>
  </Dialog_Settings>
  <License_Manager_Settings>
    <Individual_Licensing_Bahvior>false</Individual_Licensing_Bahvior>
    <License_Evaluation_Enabled>false</License_Evaluation_Enabled>
    <License_Evaluation_Type>Trial_Days</License_Evaluation_Type>
    <License_Evaluation_Time>15</License_Evaluation_Time>
    <License_Expiry_Date_Enabled>false</License_Expiry_Date_Enabled>
    <License_Expiry_Date>20250806</License_Expiry_Date>
    <License_Hardware_Lock_Enabled>true</License_Hardware_Lock_Enabled>
    <License_Hardware_BOARD>true</License_Hardware_BOARD>
    <License_Hardware_CPU>true</License_Hardware_CPU>
    <License_Hardware_HDD>true</License_Hardware_HDD>
    <License_Hardware_MAC>false</License_Hardware_MAC>
    <License_Hardware_ID>0EB3-FD5E-43A7-DD60-76EE</License_Hardware_ID>
    <License_Number_Of_Uses_Enabled>false</License_Number_Of_Uses_Enabled>
    <License_Number_Of_Uses>10</License_Number_Of_Uses>
    <License_Number_Of_Instances_Enabled>false</License_Number_Of_Instances_Enabled>
    <License_Number_Of_Instances>5</License_Number_Of_Instances>
    <AdditonalLicenseInformation>
      <KeyValue_Item>
        <Key>Company</Key>
        <Value>XYZ Limited</Value>
      </KeyValue_Item>
    </AdditonalLicenseInformation>
    <MasterKey>SETyR2Gy3Cq6lxTNTzRkeGs8TUS/cKaJp3nLslxI0f4=|&lt;RSAKeyValue&gt;&lt;Modulus&gt;saiCDDETUqRG07KJ/lhFUVVbHufUi2yCogBDOY0lwF9tPy2PTVsoCI0977RKhF/TULMcqnUVT+og9zJvb9TEjpv1tLgBOczSK+c4QC+e3LMhTbWbfycnReLPeRRICsdvOLQZtZ3Mm1OeOUXX4T2lsPPeUaA5SIfRD6RPL5u5nUE=&lt;/Modulus&gt;&lt;Exponent&gt;AQAB&lt;/Exponent&gt;&lt;P&gt;1MifGi92G3AoARkTd1GqocADUE8JgwTmurwMBg8IB82uLR+Cu3afnTd2WQxWSaZmN1tg1uJGEd6WGP6jixomXw==&lt;/P&gt;&lt;Q&gt;1b2XDfbmo8/fp9BrEjGDnrSeMe5IPCseKXv0aomyp/IY8ChhALQoDl4uEGzU3lIktWJB75nq8ZSnI+0+kzagXw==&lt;/Q&gt;&lt;DP&gt;1ElzskQ3qRg0USQbb6difQ18QNsZukVhjFugA0trz508cHhHjY/Dnq5+vEi6ra/9uqzH6JrHHXzccwNZVqY4MQ==&lt;/DP&gt;&lt;DQ&gt;JtEdo72ZOVrBJXrmVxGC88mdiTfbCoJSIfqO8uI6/eErwkGzDCursqBMAVOifRWx8Pb9PoRXdLQofo/Rc27SRQ==&lt;/DQ&gt;&lt;InverseQ&gt;CQ2xXZ8M8MiRq9pjIjChiLxNuWQ8NKZhdPDz941QO61+Gv7lWTthUMGdmloKBNoG/EnA5gGlSj8PLri4oE6aKg==&lt;/InverseQ&gt;&lt;D&gt;i6AnDVPontPTgn/YaliPqhuOcNy9NBLFHHNeYjXnjKizIKs67A4PiaEQI3sr8ZuqZctPZTx90n/tBrrR0C+FhkweV3bSCVeoGuoJVbKqINGpmfEJPGBwd9lfzIXAvA7RaVGid/SADewLSotjuHGS0sdZTXJ9ZaU3Y6yz4+wDYE0=&lt;/D&gt;&lt;/RSAKeyValue&gt;</MasterKey>
  </License_Manager_Settings>
</Reactor_Project>