// 全局配置
const API_BASE_URL = '/api/license';
const AUTH_URL = '/api/auth/login';
const LICENSE_GEN_URL = '/api/license-gen';
let authToken = localStorage.getItem('license_auth_token');
let allLicenses = [];
let availableProjects = [];
let generatedLicenses = [];

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 初始化页面
    initApp();
    
    // 注册事件监听
    setupEventListeners();
});

// 初始化应用程序
function initApp() {
    // 检查是否已登录
    if (!authToken) {
        showLoginModal();
    } else {
        hideLoading();
        loadDashboard();
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 侧边栏导航
    document.querySelectorAll('.sidebar-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = link.getAttribute('data-page');
            navigateTo(page);
        });
    });
    
    // 登录按钮
    document.getElementById('login-btn').addEventListener('click', handleLogin);
    
    // 退出登录
    document.getElementById('logout-btn').addEventListener('click', handleLogout);
    
    // 过期天数筛选
    document.getElementById('expiring-days').addEventListener('change', () => {
        loadExpiringLicenses();
    });
    
    // 授权搜索
    document.getElementById('license-search').addEventListener('input', filterLicenses);
    
    // 清除数据按钮
    const clearDataBtn = document.getElementById('clear-data-btn');
    if (clearDataBtn) {
        console.log('找到清除数据按钮，正在绑定点击事件');
        clearDataBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('清除数据按钮被点击');
            showClearDataModal();
        });
    } else {
        console.error('未找到清除数据按钮元素');
    }
    
    // 清除数据确认输入框
    const clearDataConfirm = document.getElementById('clear-data-confirm');
    if (clearDataConfirm) {
        console.log('找到清除数据确认输入框，正在绑定输入事件');
        clearDataConfirm.addEventListener('input', function() {
            console.log('清除数据确认输入框内容变化:', this.value);
            validateClearDataConfirm();
        });
    } else {
        console.error('未找到清除数据确认输入框元素');
    }
    
    // 清除数据确认按钮
    const confirmClearDataBtn = document.getElementById('confirm-clear-data-btn');
    if (confirmClearDataBtn) {
        console.log('找到清除数据确认按钮，正在绑定点击事件');
        confirmClearDataBtn.addEventListener('click', function() {
            console.log('清除数据确认按钮被点击');
            handleClearData();
        });
    } else {
        console.error('未找到清除数据确认按钮元素');
    }
    
    // License generation events
    setupLicenseGenEvents();
}

// 页面导航
function navigateTo(page) {
    // 更新活动链接
    document.querySelectorAll('.sidebar-link').forEach(link => {
        if (link.getAttribute('data-page') === page) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
    
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(content => {
        content.classList.add('d-none');
    });
    
    // 显示目标页面
    document.getElementById(`page-${page}`).classList.remove('d-none');
    
    // 加载页面数据
    switch (page) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'licenses':
            loadAllLicenses();
            break;
        case 'expiring':
            loadExpiringLicenses();
            break;
        case 'expired':
            loadExpiredLicenses();
            break;
        case 'license-gen':
            loadLicenseGenPage();
            break;
    }
}

// 加载控制面板数据
async function loadDashboard() {
    showLoading();
    
    try {
        // 获取所有授权
        const response = await fetchWithAuth(`${API_BASE_URL}/list`);
        const licenses = await response.json();
        allLicenses = licenses;
        
        // 更新统计信息
        updateDashboardStats(licenses);
        
        // 更新最近上报表格
        updateRecentReportsTable(licenses.slice(0, 10));
        
        hideLoading();
    } catch (error) {
        console.error('加载控制面板失败:', error);
        if (error.status === 401) {
            showLoginModal();
        }
        hideLoading();
    }
}

// 加载所有授权数据
async function loadAllLicenses() {
    showLoading();
    
    try {
        // 获取所有授权
        const response = await fetchWithAuth(`${API_BASE_URL}/list`);
        const licenses = await response.json();
        allLicenses = licenses;
        
        // 更新授权表格
        updateLicenseTable(licenses);
        
        hideLoading();
    } catch (error) {
        console.error('加载授权列表失败:', error);
        if (error.status === 401) {
            showLoginModal();
        }
        hideLoading();
    }
}

// 加载即将过期的授权
async function loadExpiringLicenses() {
    showLoading();
    
    try {
        // 获取过期天数阈值
        const days = document.getElementById('expiring-days').value;
        
        // 获取即将过期的授权
        const response = await fetchWithAuth(`${API_BASE_URL}/expiring?days=${days}`);
        const licenses = await response.json();
        
        // 更新表格
        updateExpiringTable(licenses);
        
        hideLoading();
    } catch (error) {
        console.error('加载即将过期授权失败:', error);
        if (error.status === 401) {
            showLoginModal();
        }
        hideLoading();
    }
}

// 加载已过期的授权
async function loadExpiredLicenses() {
    showLoading();
    
    try {
        // 获取已过期的授权
        const response = await fetchWithAuth(`${API_BASE_URL}/expired`);
        const licenses = await response.json();
        
        // 更新表格
        updateExpiredTable(licenses);
        
        hideLoading();
    } catch (error) {
        console.error('加载已过期授权失败:', error);
        if (error.status === 401) {
            showLoginModal();
        }
        hideLoading();
    }
}

// 更新控制面板统计信息
function updateDashboardStats(licenses) {
    const now = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(now.getDate() + 30);
    
    // 计算统计信息
    const total = licenses.length;
    
    const valid = licenses.filter(license => 
        license.isLicensed && 
        (!license.expirationDateLockEnabled || 
         new Date(license.expirationDate) > now)
    ).length;
    
    const expiring = licenses.filter(license => 
        license.isLicensed && 
        license.expirationDateLockEnabled && 
        new Date(license.expirationDate) <= thirtyDaysFromNow &&
        new Date(license.expirationDate) > now
    ).length;
    
    const expired = licenses.filter(license => 
        !license.isLicensed || 
        (license.expirationDateLockEnabled && 
         new Date(license.expirationDate) <= now)
    ).length;
    
    // 更新DOM
    document.getElementById('total-licenses').textContent = total;
    document.getElementById('valid-licenses').textContent = valid;
    document.getElementById('expiring-licenses').textContent = expiring;
    document.getElementById('expired-licenses').textContent = expired;
}

// 更新最近上报表格
function updateRecentReportsTable(licenses) {
    const tbody = document.getElementById('recent-reports');
    tbody.innerHTML = '';
    
    if (licenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无数据</td></tr>';
        return;
    }
    
    licenses.forEach(license => {
        const row = document.createElement('tr');
        
        // 判断状态样式
        let statusClass = '';
        let statusText = '';
        
        if (!license.isLicensed) {
            statusClass = 'text-danger';
            statusText = '未授权';
        } else if (license.expirationDateLockEnabled && new Date(license.expirationDate) < new Date()) {
            statusClass = 'text-danger';
            statusText = '已过期';
        } else if (license.expirationDateLockEnabled) {
            const daysLeft = Math.ceil((new Date(license.expirationDate) - new Date()) / (1000 * 60 * 60 * 24));
            if (daysLeft <= 30) {
                statusClass = 'text-warning';
                statusText = `${daysLeft}天后到期`;
            } else {
                statusClass = 'text-success';
                statusText = '有效';
            }
        } else {
            statusClass = 'text-success';
            statusText = '有效';
        }
        
        // 构建行内容
        row.innerHTML = `
            <td class="col-device-name">${license.machineName}</td>
            <td class="col-hardware-id"><span class="hardware-id">${license.hardwareId}</span></td>
            <td class="col-status"><span class="${statusClass}">${statusText}</span></td>
            <td class="col-expire">${license.expirationDateLockEnabled ? formatDate(license.expirationDate) : '无限期'}</td>
            <td class="col-report-time">${formatDate(license.reportTime)}</td>
            <td class="col-actions">
                <button class="btn btn-sm btn-primary" onclick="viewLicenseDetail('${license.machineId}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// 更新授权表格
function updateLicenseTable(licenses) {
    const tbody = document.getElementById('license-list');
    tbody.innerHTML = '';
    
    if (licenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';
        return;
    }
    
    licenses.forEach(license => {
        const row = document.createElement('tr');
        
        // 判断状态样式
        let statusClass = '';
        let statusText = '';
        
        if (!license.isLicensed) {
            statusClass = 'text-danger';
            statusText = '未授权';
        } else if (license.expirationDateLockEnabled && new Date(license.expirationDate) < new Date()) {
            statusClass = 'text-danger';
            statusText = '已过期';
        } else if (license.expirationDateLockEnabled) {
            const daysLeft = Math.ceil((new Date(license.expirationDate) - new Date()) / (1000 * 60 * 60 * 24));
            if (daysLeft <= 30) {
                statusClass = 'text-warning';
                statusText = `${daysLeft}天后到期`;
            } else {
                statusClass = 'text-success';
                statusText = '有效';
            }
        } else {
            statusClass = 'text-success';
            statusText = '有效';
        }
        
        // 构建行内容
        row.innerHTML = `
            <td class="col-device-name">${license.machineName}</td>
            <td class="col-hardware-id"><span class="hardware-id">${license.hardwareId}</span></td>
            <td class="col-ip">${license.ipAddress}</td>
            <td class="col-status"><span class="${statusClass}">${statusText}</span></td>
            <td class="col-expire">${license.expirationDateLockEnabled ? formatDate(license.expirationDate) : '无限期'}</td>
            <td class="col-report-time">${formatDate(license.reportTime)}</td>
            <td class="col-actions">
                <button class="btn btn-sm btn-primary" onclick="viewLicenseDetail('${license.machineId}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// 更新即将过期表格
function updateExpiringTable(licenses) {
    const tbody = document.getElementById('expiring-list');
    tbody.innerHTML = '';
    
    if (licenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无数据</td></tr>';
        return;
    }
    
    licenses.forEach(license => {
        const row = document.createElement('tr');
        
        // 计算剩余天数
        const daysLeft = Math.ceil((new Date(license.expirationDate) - new Date()) / (1000 * 60 * 60 * 24));
        
        // 构建行内容
        row.innerHTML = `
            <td class="col-device-name">${license.machineName}</td>
            <td class="col-hardware-id"><span class="hardware-id">${license.hardwareId}</span></td>
            <td class="col-ip">${license.ipAddress}</td>
            <td class="col-expire">
                ${formatDate(license.expirationDate)}
                <span class="badge bg-warning ms-2">${daysLeft}天后到期</span>
            </td>
            <td class="col-actions">
                <button class="btn btn-sm btn-primary" onclick="viewLicenseDetail('${license.machineId}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// 更新已过期表格
function updateExpiredTable(licenses) {
    const tbody = document.getElementById('expired-list');
    tbody.innerHTML = '';
    
    if (licenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无数据</td></tr>';
        return;
    }
    
    licenses.forEach(license => {
        const row = document.createElement('tr');
        
        // 计算过期天数
        const daysPassed = Math.floor((new Date() - new Date(license.expirationDate)) / (1000 * 60 * 60 * 24));
        
        // 构建行内容
        row.innerHTML = `
            <td class="col-device-name">${license.machineName}</td>
            <td class="col-hardware-id"><span class="hardware-id">${license.hardwareId}</span></td>
            <td class="col-ip">${license.ipAddress}</td>
            <td class="col-expire">
                ${formatDate(license.expirationDate)}
                <span class="badge bg-danger ms-2">已过期${daysPassed}天</span>
            </td>
            <td class="col-actions">
                <button class="btn btn-sm btn-primary" onclick="viewLicenseDetail('${license.machineId}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// 查看许可证详情
function viewLicenseDetail(machineId) {
    showLoading();
    
    console.log('查看详情，机器ID:', machineId);
    
    try {
        // 直接从服务器获取最新数据
        fetchWithAuth(`${API_BASE_URL}/status/${machineId}`)
            .then(response => {
                console.log('获取详情API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`服务器返回状态: ${response.status}`);
                }
                return response.json();
            })
            .then(license => {
                console.log('成功获取详情数据:', license);
                fillLicenseDetailModal(license);
                
                // 显示模态框
                const detailModal = new bootstrap.Modal(document.getElementById('licenseDetailModal'));
                detailModal.show();
                
                hideLoading();
            })
            .catch(error => {
                console.error('获取授权详情失败:', error);
                hideLoading();
                alert('获取授权详情失败: ' + error.message);
            });
    } catch (error) {
        console.error('查看授权详情出错:', error);
        hideLoading();
        alert('查看授权详情出错: ' + error.message);
    }
}

// 填充授权详情模态框
function fillLicenseDetailModal(license) {
    console.log('正在填充详情模态框:', license);
    
    if (!license) {
        console.error('没有有效的授权数据');
        return;
    }
    
    // 基本信息
    document.getElementById('detail-machine-name').textContent = license.machineName || '-';
    document.getElementById('detail-hardware-id').textContent = license.hardwareId || '-';
    document.getElementById('detail-ip-address').textContent = license.ipAddress || '-';
    document.getElementById('detail-windows-version').textContent = license.windowsVersion || '-';
    document.getElementById('detail-app-version').textContent = license.applicationVersion || '-';
    document.getElementById('detail-report-time').textContent = formatDateTime(license.reportTime) || '-';
    
    // 授权状态
    document.getElementById('detail-is-licensed').textContent = license.isLicensed ? '✓ 有效' : '✗ 无效';
    document.getElementById('detail-is-licensed').className = license.isLicensed ? 'text-success' : 'text-danger';
    
    const hardwareMatch = license.hardwareId === license.licenseHardwareId;
    document.getElementById('detail-hardware-match').textContent = hardwareMatch ? '✓ 匹配' : '✗ 不匹配';
    document.getElementById('detail-hardware-match').className = hardwareMatch ? 'text-success' : 'text-danger';
    
    document.getElementById('detail-expiration-date').textContent = license.expirationDateLockEnabled ? 
        formatDate(license.expirationDate) : '无限期';
    
    document.getElementById('detail-evaluation-type').textContent = license.evaluationLockEnabled ?
        license.evaluationType : '无试用限制';
    
    document.getElementById('detail-current-uses').textContent = license.numberOfUsesLockEnabled ? 
        license.currentUses : '-';
    document.getElementById('detail-max-uses').textContent = license.numberOfUsesLockEnabled ? 
        license.maxUses : '-';
    
    // 锁定状态
    document.getElementById('detail-evaluation-lock').textContent = license.evaluationLockEnabled ? '已启用' : '未启用';
    document.getElementById('detail-evaluation-lock').className = license.evaluationLockEnabled ? 'text-warning' : 'text-success';
    
    document.getElementById('detail-expiration-lock').textContent = license.expirationDateLockEnabled ? '已启用' : '未启用';
    document.getElementById('detail-expiration-lock').className = license.expirationDateLockEnabled ? 'text-warning' : 'text-success';
    
    document.getElementById('detail-uses-lock').textContent = license.numberOfUsesLockEnabled ? '已启用' : '未启用';
    document.getElementById('detail-uses-lock').className = license.numberOfUsesLockEnabled ? 'text-warning' : 'text-success';
    
    document.getElementById('detail-hardware-lock').textContent = license.hardwareLockEnabled ? '已启用' : '未启用';
    document.getElementById('detail-hardware-lock').className = license.hardwareLockEnabled ? 'text-warning' : 'text-success';
    
    // 附加信息 - 优化显示格式
    const additionalInfoContainer = document.getElementById('detail-additional-info-container');
    
    try {
        if (license.additionalInfo && Object.keys(license.additionalInfo).length > 0) {
            // 生成表格和复制按钮
            let infoHtml = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">附加信息</h6>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyAdditionalInfo()">
                        <i class="bi bi-clipboard"></i> 复制全部
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="detail-additional-info">
                        <thead>
                            <tr class="table-secondary">
                                <th style="width: 30%">键 (Key)</th>
                                <th style="width: 70%">值 (Value)</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            // 创建一个全局变量存储附加信息，用于复制功能
            window.currentAdditionalInfo = {};
            
            // 对键值对进行排序
            const sortedKeys = Object.keys(license.additionalInfo).sort();
            
            // 添加每一行
            for (const key of sortedKeys) {
                const value = license.additionalInfo[key];
                window.currentAdditionalInfo[key] = value;
                infoHtml += `
                    <tr>
                        <td class="font-monospace">${key}</td>
                        <td class="font-monospace">${value}</td>
                    </tr>
                `;
            }
            
            infoHtml += `
                        </tbody>
                    </table>
                </div>
                <div class="form-text text-muted small">点击"复制全部"按钮可以复制所有键值对</div>
            `;
            
            additionalInfoContainer.innerHTML = infoHtml;
        } else {
            additionalInfoContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 无附加信息
                </div>
            `;
        }
    } catch (error) {
        console.error('处理附加信息时出错:', error);
        additionalInfoContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 处理附加信息时出错: ${error.message}
            </div>
        `;
    }
    
    console.log('详情模态框填充完成');
}

// 复制附加信息到剪贴板
function copyAdditionalInfo() {
    if (!window.currentAdditionalInfo) {
        alert('没有可复制的附加信息');
        return;
    }
    
    try {
        // 将信息格式化为易读的文本
        let text = '';
        for (const [key, value] of Object.entries(window.currentAdditionalInfo)) {
            text += `${key}: ${value}\n`;
        }
        
        // 复制到剪贴板
        navigator.clipboard.writeText(text).then(() => {
            // 显示复制成功的临时提示
            const button = document.querySelector('[onclick="copyAdditionalInfo()"]');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i> 已复制';
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-success');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-primary');
            }, 2000);
        });
    } catch (error) {
        console.error('复制附加信息失败:', error);
        alert('复制失败: ' + error.message);
    }
}

// 筛选授权列表
function filterLicenses() {
    const searchTerm = document.getElementById('license-search').value.toLowerCase();
    
    if (!searchTerm) {
        updateLicenseTable(allLicenses);
        return;
    }
    
    const filteredLicenses = allLicenses.filter(license => 
        license.machineName.toLowerCase().includes(searchTerm) || 
        license.hardwareId.toLowerCase().includes(searchTerm) ||
        license.ipAddress.toLowerCase().includes(searchTerm)
    );
    
    updateLicenseTable(filteredLicenses);
}

// 处理登录
async function handleLogin() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        showLoginError('请输入用户名和密码');
        return;
    }
    
    showLoading();
    
    try {
        // 发送登录请求
        const response = await fetch(AUTH_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });
        
        // 处理响应
        if (response.ok) {
            const data = await response.json();
            
            // 保存令牌并关闭登录框
            localStorage.setItem('license_auth_token', data.token);
            authToken = data.token;
            hideLoginModal();
            
            // 重置错误提示
            document.getElementById('login-error').classList.add('d-none');
            
            // 加载控制面板
            loadDashboard();
        } else {
            const errorData = await response.json();
            showLoginError(errorData.message || '登录失败，请检查用户名和密码');
        }
    } catch (error) {
        console.error('登录失败:', error);
        showLoginError('登录失败，请重试');
    }
    
    hideLoading();
}

// 处理退出登录
function handleLogout() {
    localStorage.removeItem('license_auth_token');
    authToken = null;
    showLoginModal();
}

// 带授权令牌的fetch
async function fetchWithAuth(url, options = {}) {
    if (!authToken) {
        throw new Error('未授权');
    }
    
    const headers = options.headers || {};
    headers.Authorization = `Bearer ${authToken}`;
    
    const response = await fetch(url, {
        ...options,
        headers
    });
    
    if (response.status === 401) {
        localStorage.removeItem('license_auth_token');
        authToken = null;
        showLoginModal();
        throw { status: 401, message: '未授权' };
    }
    
    return response;
}

// 显示登录框
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
    hideLoading();
}

// 隐藏登录框
function hideLoginModal() {
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }
}

// 显示登录错误
function showLoginError(message) {
    const loginError = document.getElementById('login-error');
    loginError.textContent = message;
    loginError.classList.remove('d-none');
}

// 显示加载中
function showLoading() {
    document.getElementById('loading').style.display = 'flex';
}

// 隐藏加载中
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// 格式化日期（北京时间 UTC+8）
function formatDate(dateString) {
    if (!dateString) return '-';
    
    // 解析日期字符串
    const date = new Date(dateString);
    
    // 转换为北京时间（UTC+8）
    const year = date.getFullYear();
    const month = padZero(date.getMonth() + 1);
    const day = padZero(date.getDate());
    
    // 返回北京时间日期格式
    return `${year}-${month}-${day}`;
}

// 格式化日期时间（北京时间 UTC+8）
function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    // 解析日期字符串
    const date = new Date(dateString);
    
    // 获取年月日时分
    const year = date.getFullYear();
    const month = padZero(date.getMonth() + 1);
    const day = padZero(date.getDate());
    
    // 获取时分，调整为UTC+8时区
    const hours = padZero(date.getHours());
    const minutes = padZero(date.getMinutes());
    
    // 返回北京时间日期和时间格式，精确到分钟
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 补零
function padZero(num) {
    return num.toString().padStart(2, '0');
}

// 截断文字
function truncateText(text, length) {
    if (!text) return '-';
    if (text.length <= length) return text;
    return text.substring(0, length) + '...';
}

// 显示清除数据确认对话框
function showClearDataModal() {
    // 重置确认输入框
    document.getElementById('clear-data-confirm').value = '';
    document.getElementById('confirm-clear-data-btn').disabled = true;
    
    // 显示对话框
    const clearDataModal = document.getElementById('clear-data-modal');
    if (clearDataModal) {
        console.log('找到清除数据模态框元素');
        const modal = new bootstrap.Modal(clearDataModal);
        modal.show();
        console.log('已尝试显示清除数据模态框');
    } else {
        console.error('未找到清除数据模态框元素');
        alert('系统错误：未找到清除数据确认对话框');
    }
}

// 验证清除数据确认输入
function validateClearDataConfirm() {
    const confirmInput = document.getElementById('clear-data-confirm').value;
    const confirmButton = document.getElementById('confirm-clear-data-btn');
    
    console.log('清除数据确认输入:', confirmInput);
    
    // 验证输入是否为"CONFIRM"
    const isValid = confirmInput === 'CONFIRM';
    confirmButton.disabled = !isValid;
    
    if (isValid) {
        console.log('验证通过，确认按钮已启用');
        confirmButton.classList.remove('btn-secondary');
        confirmButton.classList.add('btn-danger');
    } else {
        console.log('验证未通过，确认按钮已禁用');
        confirmButton.classList.remove('btn-danger');
        confirmButton.classList.add('btn-secondary');
    }
}

// 处理清除数据操作
async function handleClearData() {
    console.log('开始执行清除数据操作');
    showLoading();
    
    try {
        console.log('发送清除数据API请求');
        // 调用清除数据API
        const response = await fetchWithAuth(`${API_BASE_URL}/clear-all`, {
            method: 'DELETE'
        });
        
        console.log('收到API响应:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API返回错误:', response.status, errorText);
            throw new Error(`服务器返回错误: ${response.status} ${errorText}`);
        }
        
        const result = await response.json();
        console.log('清除数据结果:', result);
        
        // 隐藏模态框
        const clearDataModal = document.getElementById('clear-data-modal');
        if (clearDataModal) {
            const modal = bootstrap.Modal.getInstance(clearDataModal);
            if (modal) {
                modal.hide();
                console.log('清除数据模态框已隐藏');
            } else {
                console.error('无法获取模态框实例');
            }
        } else {
            console.error('未找到清除数据模态框元素');
        }
        
        // 显示成功消息
        //alert(`数据清除成功！已删除 ${result.deletedCount} 条记录。`);
        
        // 重新加载当前页面数据
        const activeLink = document.querySelector('.sidebar-link.active');
        if (activeLink) {
            const activePage = activeLink.getAttribute('data-page');
            console.log('重新加载页面:', activePage);
            navigateTo(activePage);
        } else {
            console.error('未找到活动页面');
            loadDashboard(); // 默认加载控制面板
        }
        
    } catch (error) {
        console.error('清除数据失败:', error);
        alert('清除数据失败: ' + (error.message || '未知错误'));
    } finally {
        hideLoading();
        console.log('清除数据操作完成');
    }
}

// License generation functions
function setupLicenseGenEvents() {
    // License generation form
    const licenseGenForm = document.getElementById('license-gen-form');
    if (licenseGenForm) {
        licenseGenForm.addEventListener('submit', handleLicenseGenSubmit);
    }

    // Setup config item toggles
    setupConfigItemToggles();
    
    // Save license button
    const saveLicenseBtn = document.getElementById('save-license-btn');
    if (saveLicenseBtn) {
        saveLicenseBtn.addEventListener('click', handleSaveLicense);
    }
    
    // Test project button
    const testProjectBtn = document.getElementById('test-project-btn');
    if (testProjectBtn) {
        testProjectBtn.addEventListener('click', handleTestProject);
    }
    
    // Refresh projects button
    const refreshProjectsBtn = document.getElementById('refresh-projects-btn');
    if (refreshProjectsBtn) {
        refreshProjectsBtn.addEventListener('click', () => {
            console.log('手动刷新项目文件...');
            loadAvailableProjects();
        });
    }
    
    // Clear form button
    const clearFormBtn = document.getElementById('clear-form-btn');
    if (clearFormBtn) {
        clearFormBtn.addEventListener('click', clearLicenseGenForm);
    }
    
    // Add additional info button
    const addInfoBtn = document.getElementById('add-info-btn');
    if (addInfoBtn) {
        addInfoBtn.addEventListener('click', addAdditionalInfoRow);
    }
    
    // Remove info buttons (event delegation)
    document.addEventListener('click', (e) => {
        if (e.target.closest('.remove-info-btn')) {
            removeAdditionalInfoRow(e.target.closest('.additional-info-row'));
        }
    });
}

// Load license generation page
async function loadLicenseGenPage() {
    console.log('开始加载授权生成页面...');
    showLoading();
    
    try {
        // Check DLL status
        console.log('检查DLL状态...');
        await checkDllStatus();
        
        // Load available projects
        console.log('加载可用项目...');
        await loadAvailableProjects();
        
        // Load generated licenses
        console.log('加载已生成的授权...');
        await loadGeneratedLicenses();
        
        console.log('授权生成页面加载完成');
        hideLoading();
    } catch (error) {
        console.error('加载授权生成页面失败:', error);
        if (error.status === 401) {
            showLoginModal();
        }
        hideLoading();
    }
}

// Check LicenseGen.dll status
async function checkDllStatus() {
    try {
        const response = await fetchWithAuth(`${LICENSE_GEN_URL}/inspect-dll`);
        const result = await response.json();
        
        const statusElement = document.getElementById('dll-status');
        if (result.success) {
            statusElement.innerHTML = '<i class="bi bi-check-circle"></i> LicenseGen.dll 正常加载';
            statusElement.className = 'alert alert-success';
        } else {
            statusElement.innerHTML = '<i class="bi bi-x-circle"></i> LicenseGen.dll 加载失败';
            statusElement.className = 'alert alert-danger';
        }
    } catch (error) {
        console.error('检查DLL状态失败:', error);
        const statusElement = document.getElementById('dll-status');
        statusElement.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 无法检查DLL状态';
        statusElement.className = 'alert alert-warning';
    }
}

// Load available projects
async function loadAvailableProjects() {
    try {
        console.log('开始加载项目文件...');
        const response = await fetchWithAuth(`${LICENSE_GEN_URL}/projects`);
        console.log('项目文件API响应状态:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const projects = await response.json();
        console.log('获取到的项目文件:', projects);
        availableProjects = projects;
        
        const selectElement = document.getElementById('project-file');
        if (!selectElement) {
            console.error('未找到项目文件选择框元素');
            return;
        }
        
        selectElement.innerHTML = '<option value="">选择项目文件...</option>';
        
        if (projects.length === 0) {
            selectElement.innerHTML = '<option value="">暂无项目文件</option>';
            return;
        }
        
        projects.forEach(project => {
            const option = document.createElement('option');
            option.value = project.fullPath;
            option.textContent = project.fileName;
            selectElement.appendChild(option);
        });
        
        console.log('项目文件加载完成，共', projects.length, '个项目');
    } catch (error) {
        console.error('加载项目文件失败:', error);
        
        // 显示错误信息在页面上
        const selectElement = document.getElementById('project-file');
        if (selectElement) {
            selectElement.innerHTML = '<option value="">加载失败</option>';
        }
        
        // 不使用alert，而是在控制台显示错误
        console.error('项目文件加载错误详情:', error);
    }
}

// Load generated licenses
async function loadGeneratedLicenses() {
    try {
        const response = await fetchWithAuth(`${LICENSE_GEN_URL}/generated-licenses`);
        const licenses = await response.json();
        generatedLicenses = licenses;
        
        updateGeneratedLicensesList();
    } catch (error) {
        console.error('加载已生成的授权失败:', error);
        // Don't show alert for this as it might be empty
    }
}

// Update generated licenses list
function updateGeneratedLicensesList() {
    const container = document.getElementById('generated-licenses-list');
    
    if (generatedLicenses.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-folder2-open fs-1"></i>
                <p>暂无已生成的授权文件</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="list-group">';
    generatedLicenses.forEach(license => {
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${license.fileName}</h6>
                        <small class="text-muted">
                            ${formatFileSize(license.fileSize)} • ${formatDateTime(license.createdTime)}
                        </small>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="downloadLicense('${license.fullPath}')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

// Handle license generation form submission
async function handleLicenseGenSubmit(e) {
    e.preventDefault();
    
    try {
        const requestData = buildLicenseGenRequest();
        
        showLoading();
        
        const response = await fetchWithAuth(`${LICENSE_GEN_URL}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (response.ok) {
            // Download the file
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = requestData.outputFileName || 'license.license';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            // Refresh generated licenses list
            await loadGeneratedLicenses();
            
            //alert('授权文件生成成功！');
        } else {
            const error = await response.json();
            alert('生成失败: ' + error.message);
        }
    } catch (error) {
        console.error('生成授权文件失败:', error);
        alert('生成授权文件失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Handle save license to server
async function handleSaveLicense() {
    try {
        const requestData = buildLicenseGenRequest();
        
        showLoading();
        
        const response = await fetchWithAuth(`${LICENSE_GEN_URL}/generate-and-save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Refresh generated licenses list
            await loadGeneratedLicenses();
            
            alert('授权文件生成并保存成功！\n文件名: ' + result.fileName);
        } else {
            alert('保存失败: ' + result.message);
        }
    } catch (error) {
        console.error('保存授权文件失败:', error);
        alert('保存授权文件失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Handle test project
async function handleTestProject() {
    const projectFile = document.getElementById('project-file').value;
    
    if (!projectFile) {
        alert('请先选择项目文件');
        return;
    }
    
    showLoading();
    
    try {
        const response = await fetchWithAuth(`${LICENSE_GEN_URL}/test-project`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ projectFilePath: projectFile })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('项目文件测试成功！');
        } else {
            alert('项目文件测试失败: ' + result.message);
        }
    } catch (error) {
        console.error('测试项目文件失败:', error);
        alert('测试项目文件失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Build license generation request
function buildLicenseGenRequest() {
    const projectFile = document.getElementById('project-file').value;
    const hardwareId = document.getElementById('hardware-id').value;
    const expirationDate = document.getElementById('expiration-date').value;
    const evaluationTime = document.getElementById('evaluation-time').value;
    const maxUses = document.getElementById('max-uses').value;
    const maxInstances = document.getElementById('max-instances').value;
    const outputFileName = document.getElementById('output-filename').value;
    
    if (!projectFile) {
        throw new Error('请选择项目文件');
    }

    // 验证硬件锁配置
    const hardwareLockEnabled = document.getElementById('hardware-lock-enabled').checked;
    if (hardwareLockEnabled && !hardwareId.trim()) {
        throw new Error('启用硬件锁时必须输入硬件ID');
    }
    
    const request = {
        projectFilePath: projectFile,
        hardwareId: hardwareId,
        hardwareLockEnabled: document.getElementById('hardware-lock-enabled').checked,
        expirationLockEnabled: document.getElementById('expiration-lock-enabled').checked,
        evaluationLockEnabled: document.getElementById('evaluation-lock-enabled').checked,
        numberOfUsesLockEnabled: document.getElementById('uses-lock-enabled').checked,
        numberOfInstancesLockEnabled: document.getElementById('instances-lock-enabled').checked,
        additionalInfo: {}
    };
    
    if (expirationDate) {
        request.expirationDate = new Date(expirationDate).toISOString();
    }
    
    if (evaluationTime) {
        request.evaluationTime = parseInt(evaluationTime);
    }
    
    if (maxUses) {
        request.maxUses = parseInt(maxUses);
    }
    
    if (maxInstances) {
        request.maxInstances = parseInt(maxInstances);
    }
    
    if (outputFileName) {
        request.outputFileName = outputFileName;
    }
    
    // Collect additional info
    const infoRows = document.querySelectorAll('.additional-info-row');
    infoRows.forEach(row => {
        const key = row.querySelector('.additional-key').value.trim();
        const value = row.querySelector('.additional-value').value.trim();
        if (key && value) {
            request.additionalInfo[key] = value;
        }
    });
    
    return request;
}

// Add additional info row
function addAdditionalInfoRow() {
    const container = document.getElementById('additional-info-container');
    const row = document.createElement('div');
    row.className = 'row additional-info-row mb-2';
    row.innerHTML = `
        <div class="col-md-5">
            <input type="text" class="form-control additional-key" placeholder="键">
        </div>
        <div class="col-md-5">
            <input type="text" class="form-control additional-value" placeholder="值">
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm remove-info-btn">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(row);
}

// Remove additional info row
function removeAdditionalInfoRow(row) {
    row.remove();
}

// Clear license generation form
function clearLicenseGenForm() {
    document.getElementById('license-gen-form').reset();

    // Clear additional info rows except the first one
    const container = document.getElementById('additional-info-container');
    const rows = container.querySelectorAll('.additional-info-row');
    for (let i = 1; i < rows.length; i++) {
        rows[i].remove();
    }

    // Clear the first row
    const firstRow = container.querySelector('.additional-info-row');
    if (firstRow) {
        firstRow.querySelector('.additional-key').value = '';
        firstRow.querySelector('.additional-value').value = '';
    }

    // Reset checkboxes to default state (all disabled)
    const checkboxes = [
        'hardware-lock-enabled',
        'expiration-lock-enabled',
        'evaluation-lock-enabled',
        'uses-lock-enabled',
        'instances-lock-enabled'
    ];

    checkboxes.forEach(checkboxId => {
        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
            checkbox.checked = false;
            // 触发change事件以更新UI状态
            checkbox.dispatchEvent(new Event('change'));
        }
    });
}

// Download license file
function downloadLicense(filePath) {
    // This is a simplified version - in a real app, you'd need a proper download endpoint
    alert('下载功能需要额外的API端点支持');
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Debug function to test API directly
async function testProjectsApi() {
    try {
        console.log('测试项目文件API...');
        const token = localStorage.getItem('license_auth_token');
        console.log('当前token:', token ? '已设置' : '未设置');
        
        const response = await fetch(`${LICENSE_GEN_URL}/projects`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('API响应状态:', response.status);
        console.log('响应头:', response.headers);
        
        if (response.ok) {
            const data = await response.json();
            console.log('API响应数据:', data);
            return data;
        } else {
            const errorText = await response.text();
            console.error('API错误响应:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
    } catch (error) {
        console.error('API测试失败:', error);
        throw error;
    }
}

// 设置到期日期快捷功能
function setExpirationDate(months) {
    const expirationDateInput = document.getElementById('expiration-date');
    const expirationLockEnabled = document.getElementById('expiration-lock-enabled');
    
    if (months === 0) {
        // 永久授权 - 设置一个非常远的日期
        expirationDateInput.value = '2099-12-31';
        expirationLockEnabled.checked = false; // 永久授权通常不需要过期锁
    } else {
        // 计算指定月数后的日期
        const today = new Date();
        const futureDate = new Date(today.getFullYear(), today.getMonth() + months, today.getDate());
        
        // 格式化日期为 YYYY-MM-DD
        const year = futureDate.getFullYear();
        const month = String(futureDate.getMonth() + 1).padStart(2, '0');
        const day = String(futureDate.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;
        
        expirationDateInput.value = formattedDate;
        expirationLockEnabled.checked = true; // 自动启用过期锁
    }
    
    // 添加视觉反馈
    const buttons = document.querySelectorAll('.btn-group .btn');
    buttons.forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-secondary');
    });
    
    // 高亮当前选中的按钮
    event.target.classList.remove('btn-outline-secondary');
    event.target.classList.add('btn-primary');
    
    console.log(`已设置到期日期: ${expirationDateInput.value} (${months === 0 ? '永久' : months + '个月'})`);
}

// Setup config item toggles
function setupConfigItemToggles() {
    // 配置项映射
    const configMappings = [
        {
            checkbox: 'hardware-lock-enabled',
            input: 'hardware-id',
            container: 'hardware-config'
        },
        {
            checkbox: 'expiration-lock-enabled',
            input: 'expiration-date',
            container: 'expiration-config',
            extraElements: ['expiration-quick-buttons']
        },
        {
            checkbox: 'evaluation-lock-enabled',
            input: 'evaluation-time',
            container: 'evaluation-config'
        },
        {
            checkbox: 'uses-lock-enabled',
            input: 'max-uses',
            container: 'uses-config'
        },
        {
            checkbox: 'instances-lock-enabled',
            input: 'max-instances',
            container: 'instances-config'
        }
    ];

    // 为每个配置项设置事件监听
    configMappings.forEach(config => {
        const checkbox = document.getElementById(config.checkbox);
        const input = document.getElementById(config.input);
        const container = document.getElementById(config.container);

        if (checkbox && input && container) {
            // 初始状态
            updateConfigItemState(checkbox, input, container, config.extraElements);

            // 监听复选框变化
            checkbox.addEventListener('change', () => {
                updateConfigItemState(checkbox, input, container, config.extraElements);
            });
        }
    });
}

// 更新配置项状态
function updateConfigItemState(checkbox, input, container, extraElements = []) {
    const isEnabled = checkbox.checked;

    // 更新输入框状态
    input.disabled = !isEnabled;

    // 更新容器样式
    if (isEnabled) {
        container.classList.add('enabled');
    } else {
        container.classList.remove('enabled');
    }

    // 特殊处理硬件锁配置
    if (checkbox.id === 'hardware-lock-enabled') {
        if (isEnabled) {
            input.setAttribute('required', 'required');
            input.placeholder = '默认值：####-####-####-####-####';
        } else {
            input.removeAttribute('required');
            input.placeholder = '启用硬件锁时必须输入';
            input.value = ''; // 清空硬件ID
        }
    }

    // 处理额外元素（如快捷按钮）
    if (extraElements) {
        extraElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = isEnabled ? 'block' : 'none';
            }
        });
    }
}