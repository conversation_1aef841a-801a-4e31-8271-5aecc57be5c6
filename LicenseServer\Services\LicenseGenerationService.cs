using System.Reflection;

namespace LicenseServer.Services
{
    public interface ILicenseGenerationService
    {
        Task<byte[]> GenerateLicenseFileAsync(LicenseGenerationRequest request);
        Task<string> GenerateLicenseFileToDiskAsync(LicenseGenerationRequest request);
        bool TestProjectFile(string projectFilePath);
        object? GetLicenseGeneratorType();
        string GetProjectFileDefaultHardwareId(string projectFilePath);
    }

    public class LicenseGenerationRequest
    {
        public string ProjectFilePath { get; set; } = "";
        public string HardwareId { get; set; } = "";
        public bool HardwareLockEnabled { get; set; } = false;
        public Dictionary<string, string> AdditionalInfo { get; set; } = new();
        public DateTime? ExpirationDate { get; set; }
        public bool ExpirationLockEnabled { get; set; }
        public int? EvaluationTime { get; set; }
        public string EvaluationType { get; set; } = "Trial_Days";
        public bool EvaluationLockEnabled { get; set; }
        public int? MaxUses { get; set; }
        public bool NumberOfUsesLockEnabled { get; set; }
        public int? MaxInstances { get; set; }
        public bool NumberOfInstancesLockEnabled { get; set; }
        public string OutputFileName { get; set; } = "";
    }

    public class LicenseGenerationService : ILicenseGenerationService
    {
        private readonly ILogger<LicenseGenerationService> _logger;
        private readonly IConfiguration _configuration;

        public LicenseGenerationService(ILogger<LicenseGenerationService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public object? GetLicenseGeneratorType()
        {
            try
            {
                // 尝试加载 LicenseGen.dll 并获取类型信息
                var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                var licenseGenType = assembly.GetType("LicenseGenerator");
                
                if (licenseGenType != null)
                {
                    _logger.LogInformation($"成功找到 LicenseGenerator 类型: {licenseGenType.FullName}");
                    
                    // 获取所有公共属性和方法
                    var properties = licenseGenType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                    var methods = licenseGenType.GetMethods(BindingFlags.Public | BindingFlags.Instance);
                    
                    _logger.LogInformation("LicenseGenerator 属性:");
                    foreach (var prop in properties)
                    {
                        _logger.LogInformation($"  - {prop.Name}: {prop.PropertyType.Name}");
                    }
                    
                    _logger.LogInformation("LicenseGenerator 方法:");
                    foreach (var method in methods)
                    {
                        _logger.LogInformation($"  - {method.Name}({string.Join(", ", method.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"))})");
                    }
                    
                    return licenseGenType;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取 LicenseGenerator 类型信息失败: {ex.Message}");
                return null;
            }
        }

        public bool TestProjectFile(string projectFilePath)
        {
            try
            {
                if (!File.Exists(projectFilePath))
                {
                    _logger.LogError($"项目文件不存在: {projectFilePath}");
                    return false;
                }

                // 尝试创建 LicenseGenerator 实例来测试项目文件
                var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                var licenseGenType = assembly.GetType("LicenseGenerator");
                
                if (licenseGenType == null)
                {
                    _logger.LogError("未找到 LicenseGenerator 类型");
                    return false;
                }

                var licenseGen = Activator.CreateInstance(licenseGenType, new object[] { projectFilePath });
                _logger.LogInformation($"项目文件测试成功: {projectFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"项目文件测试失败: {ex.Message}");
                return false;
            }
        }

        public async Task<byte[]> GenerateLicenseFileAsync(LicenseGenerationRequest request)
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (!File.Exists(request.ProjectFilePath))
                    {
                        throw new FileNotFoundException($"项目文件不存在: {request.ProjectFilePath}");
                    }

                    var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                    var licenseGenType = assembly.GetType("LicenseGenerator");
                    
                    if (licenseGenType == null)
                    {
                        throw new TypeLoadException("未找到 LicenseGenerator 类型");
                    }

                    var licenseGen = Activator.CreateInstance(licenseGenType, new object[] { request.ProjectFilePath });

                    // 记录前端请求的配置
                    _logger.LogInformation($"前端配置 - 硬件锁启用: {request.HardwareLockEnabled}, 硬件ID: '{request.HardwareId}'");
                    _logger.LogInformation($"前端配置 - 过期锁启用: {request.ExpirationLockEnabled}, 过期日期: {request.ExpirationDate}");
                    _logger.LogInformation($"前端配置 - 试用期启用: {request.EvaluationLockEnabled}, 试用时间: {request.EvaluationTime}");
                    _logger.LogInformation($"前端配置 - 使用次数锁启用: {request.NumberOfUsesLockEnabled}, 最大使用次数: {request.MaxUses}");
                    _logger.LogInformation($"前端配置 - 实例数锁启用: {request.NumberOfInstancesLockEnabled}, 最大实例数: {request.MaxInstances}");

                    // 尝试设置属性（使用反射）
                    var properties = licenseGenType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                    // 列出所有可用属性
                    _logger.LogInformation($"DLL可用属性列表:");
                    foreach (var prop in properties)
                    {
                        try
                        {
                            var currentValue = prop.GetValue(licenseGen);
                            _logger.LogInformation($"  - {prop.Name} ({prop.PropertyType.Name}): {currentValue}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"  - {prop.Name} ({prop.PropertyType.Name}): 无法读取值 - {ex.Message}");
                        }
                    }
                    
                    _logger.LogInformation("开始设置DLL属性...");

                    foreach (var prop in properties)
                    {
                        try
                        {
                            object? oldValue = null;
                            object? newValue = null;
                            bool shouldSet = false;

                            // 获取设置前的值
                            try { oldValue = prop.GetValue(licenseGen); } catch { }

                            switch (prop.Name)
                            {
                                case "Hardware_Enabled":  // 修正：原来是 HardwareLock_Enabled
                                    newValue = request.HardwareLockEnabled;
                                    shouldSet = true;
                                    break;
                                case "HardwareID":
                                    newValue = request.HardwareId ?? "";
                                    shouldSet = true;
                                    break;
                                case "Expiration_Date_Enabled":  // 修正：原来是 ExpirationDateLock_Enabled
                                    newValue = request.ExpirationLockEnabled;
                                    shouldSet = true;
                                    break;
                                case "ExpirationDate":
                                    if (request.ExpirationDate.HasValue)
                                    {
                                        newValue = request.ExpirationDate.Value;
                                        shouldSet = true;
                                    }
                                    break;
                                case "Evaluation_Enabled":  // 修正：原来是 EvaluationLock_Enabled
                                    newValue = request.EvaluationLockEnabled;
                                    shouldSet = true;
                                    break;
                                case "Evaluation_Time":  // 修正：原来是 EvaluationTime
                                    if (request.EvaluationTime.HasValue)
                                    {
                                        newValue = request.EvaluationTime.Value;
                                        shouldSet = true;
                                    }
                                    break;
                                case "Number_Of_Uses_Enabled":  // 修正：原来是 NumberOfUsesLock_Enabled
                                    newValue = request.NumberOfUsesLockEnabled;
                                    shouldSet = true;
                                    break;
                                case "Number_Of_Uses":  // 修正：原来是 NumberOfUses
                                    if (request.MaxUses.HasValue)
                                    {
                                        newValue = request.MaxUses.Value;
                                        shouldSet = true;
                                    }
                                    break;
                                case "Number_Of_Instances_Enabled":  // 修正：原来是 NumberOfInstancesLock_Enabled
                                    newValue = request.NumberOfInstancesLockEnabled;
                                    shouldSet = true;
                                    break;
                                case "Number_Of_Instances":  // 修正：原来是 NumberOfInstances
                                    if (request.MaxInstances.HasValue)
                                    {
                                        newValue = request.MaxInstances.Value;
                                        shouldSet = true;
                                    }
                                    break;
                            }

                            if (shouldSet)
                            {
                                _logger.LogInformation($"设置属性 {prop.Name}: {oldValue} -> {newValue}");
                                prop.SetValue(licenseGen, newValue);

                                // 验证设置是否成功
                                try
                                {
                                    var actualValue = prop.GetValue(licenseGen);
                                    if (actualValue?.ToString() == newValue?.ToString())
                                    {
                                        _logger.LogInformation($"✓ 属性 {prop.Name} 设置成功: {actualValue}");
                                    }
                                    else
                                    {
                                        _logger.LogWarning($"✗ 属性 {prop.Name} 设置失败: 期望 {newValue}, 实际 {actualValue}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogWarning($"无法验证属性 {prop.Name} 的设置结果: {ex.Message}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"设置属性 {prop.Name} 失败: {ex.Message}");
                        }
                    }

                    // 尝试设置附加信息
                    var addInfoProp = licenseGenType.GetProperty("AdditonalLicenseInformation");
                    if (addInfoProp != null && request.AdditionalInfo != null && request.AdditionalInfo.Count > 0)
                    {
                        try
                        {
                            var addInfo = addInfoProp.GetValue(licenseGen);
                            var addMethod = addInfo?.GetType().GetMethod("Add");
                            
                            if (addMethod != null)
                            {
                                foreach (var info in request.AdditionalInfo)
                                {
                                    addMethod.Invoke(addInfo, new object[] { info.Key, info.Value });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"设置附加信息失败: {ex.Message}");
                        }
                    }

                    // 生成授权文件到字节数组
                    _logger.LogInformation($"开始生成授权文件，项目: {Path.GetFileName(request.ProjectFilePath)}");
                    
                    // 生成临时文件
                    string tempFilePath = Path.GetTempFileName() + ".license";
                    
                    // 获取所有 CreateLicenseFile 方法信息用于调试
                    var allCreateMethods = licenseGenType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                        .Where(m => m.Name == "CreateLicenseFile")
                        .ToList();
                    
                    _logger.LogInformation($"找到 {allCreateMethods.Count} 个 CreateLicenseFile 方法:");
                    foreach (var method in allCreateMethods)
                    {
                        _logger.LogInformation($"  - {method.ReturnType.Name} {method.Name}({string.Join(", ", method.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"))})");
                    }
                    
                    // 在生成授权文件前，再次检查关键属性的最终值
                    _logger.LogInformation("生成授权文件前的最终属性值:");
                    var keyProperties = new[] { "Hardware_Enabled", "HardwareID", "Expiration_Date_Enabled", "ExpirationDate", "Evaluation_Enabled", "Evaluation_Time" };
                    foreach (var propName in keyProperties)
                    {
                        var prop = properties.FirstOrDefault(p => p.Name == propName);
                        if (prop != null)
                        {
                            try
                            {
                                var value = prop.GetValue(licenseGen);
                                _logger.LogInformation($"  {propName}: {value}");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"  {propName}: 无法读取 - {ex.Message}");
                            }
                        }
                        else
                        {
                            _logger.LogWarning($"  {propName}: 属性不存在");
                        }
                    }

                    // 尝试获取指定参数类型的方法
                    var createMethod = licenseGenType.GetMethod("CreateLicenseFile", new Type[] { typeof(string) });
                    if (createMethod != null)
                    {
                        _logger.LogInformation($"使用方法: {createMethod.ReturnType.Name} {createMethod.Name}({string.Join(", ", createMethod.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"))})");
                        createMethod.Invoke(licenseGen, new object[] { tempFilePath });
                    }
                    else
                    {
                        throw new MissingMethodException($"未找到 CreateLicenseFile(string) 方法。可用方法: {string.Join(", ", allCreateMethods.Select(m => $"{m.ReturnType.Name} {m.Name}({string.Join(", ", m.GetParameters().Select(p => p.ParameterType.Name))})"))}");
                    }
                    
                    // 读取文件内容
                    byte[] licenseBytes = File.ReadAllBytes(tempFilePath);
                    
                    // 删除临时文件
                    File.Delete(tempFilePath);
                    
                    _logger.LogInformation($"授权文件生成成功，大小: {licenseBytes.Length} 字节");
                    
                    return licenseBytes;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"生成授权文件失败: {ex.Message}");
                    throw;
                }
            });
        }

        public async Task<string> GenerateLicenseFileToDiskAsync(LicenseGenerationRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.OutputFileName))
                {
                    // 生成默认文件名
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    request.OutputFileName = $"InduVision_License_{timestamp}.license";
                }

                // 确保输出目录存在
                string? outputDir = Path.GetDirectoryName(request.OutputFileName);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                byte[] licenseBytes = await GenerateLicenseFileAsync(request);
                
                // 写入文件
                await File.WriteAllBytesAsync(request.OutputFileName, licenseBytes);
                
                _logger.LogInformation($"授权文件已保存到: {request.OutputFileName}");
                
                return request.OutputFileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存授权文件到磁盘失败: {ex.Message}");
                throw;
            }
        }

        public string GetProjectFileDefaultHardwareId(string projectFilePath)
        {
            try
            {
                if (!File.Exists(projectFilePath))
                {
                    _logger.LogWarning($"项目文件不存在: {projectFilePath}");
                    return "####-####-####-####-####";
                }

                var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                var licenseGenType = assembly.GetType("LicenseGenerator");

                if (licenseGenType == null)
                {
                    _logger.LogWarning("未找到 LicenseGenerator 类型");
                    return "####-####-####-####-####";
                }

                var licenseGen = Activator.CreateInstance(licenseGenType, new object[] { projectFilePath });

                // 获取HardwareID属性
                var hardwareIdProp = licenseGenType.GetProperty("HardwareID");
                if (hardwareIdProp != null)
                {
                    var hardwareId = hardwareIdProp.GetValue(licenseGen)?.ToString();
                    _logger.LogInformation($"从项目文件读取到默认硬件ID: {hardwareId}");
                    return hardwareId ?? "####-####-####-####-####";
                }
                else
                {
                    _logger.LogWarning("未找到 HardwareID 属性");
                    return "####-####-####-####-####";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取项目文件默认硬件ID失败: {ex.Message}");
                return "####-####-####-####-####";
            }
        }
    }
}