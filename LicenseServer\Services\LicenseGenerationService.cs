using System.Reflection;

namespace LicenseServer.Services
{
    public interface ILicenseGenerationService
    {
        Task<byte[]> GenerateLicenseFileAsync(LicenseGenerationRequest request);
        Task<string> GenerateLicenseFileToDiskAsync(LicenseGenerationRequest request);
        bool TestProjectFile(string projectFilePath);
        object? GetLicenseGeneratorType();
    }

    public class LicenseGenerationRequest
    {
        public string ProjectFilePath { get; set; } = "";
        public string HardwareId { get; set; } = "";
        public bool HardwareLockEnabled { get; set; } = true;
        public Dictionary<string, string> AdditionalInfo { get; set; } = new();
        public DateTime? ExpirationDate { get; set; }
        public bool ExpirationLockEnabled { get; set; }
        public int? EvaluationTime { get; set; }
        public string EvaluationType { get; set; } = "Trial_Days";
        public bool EvaluationLockEnabled { get; set; }
        public int? MaxUses { get; set; }
        public bool NumberOfUsesLockEnabled { get; set; }
        public int? MaxInstances { get; set; }
        public bool NumberOfInstancesLockEnabled { get; set; }
        public string OutputFileName { get; set; } = "";
    }

    public class LicenseGenerationService : ILicenseGenerationService
    {
        private readonly ILogger<LicenseGenerationService> _logger;
        private readonly IConfiguration _configuration;

        public LicenseGenerationService(ILogger<LicenseGenerationService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public object? GetLicenseGeneratorType()
        {
            try
            {
                // 尝试加载 LicenseGen.dll 并获取类型信息
                var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                var licenseGenType = assembly.GetType("LicenseGenerator");
                
                if (licenseGenType != null)
                {
                    _logger.LogInformation($"成功找到 LicenseGenerator 类型: {licenseGenType.FullName}");
                    
                    // 获取所有公共属性和方法
                    var properties = licenseGenType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                    var methods = licenseGenType.GetMethods(BindingFlags.Public | BindingFlags.Instance);
                    
                    _logger.LogInformation("LicenseGenerator 属性:");
                    foreach (var prop in properties)
                    {
                        _logger.LogInformation($"  - {prop.Name}: {prop.PropertyType.Name}");
                    }
                    
                    _logger.LogInformation("LicenseGenerator 方法:");
                    foreach (var method in methods)
                    {
                        _logger.LogInformation($"  - {method.Name}({string.Join(", ", method.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"))})");
                    }
                    
                    return licenseGenType;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取 LicenseGenerator 类型信息失败: {ex.Message}");
                return null;
            }
        }

        public bool TestProjectFile(string projectFilePath)
        {
            try
            {
                if (!File.Exists(projectFilePath))
                {
                    _logger.LogError($"项目文件不存在: {projectFilePath}");
                    return false;
                }

                // 尝试创建 LicenseGenerator 实例来测试项目文件
                var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                var licenseGenType = assembly.GetType("LicenseGenerator");
                
                if (licenseGenType == null)
                {
                    _logger.LogError("未找到 LicenseGenerator 类型");
                    return false;
                }

                var licenseGen = Activator.CreateInstance(licenseGenType, new object[] { projectFilePath });
                _logger.LogInformation($"项目文件测试成功: {projectFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"项目文件测试失败: {ex.Message}");
                return false;
            }
        }

        public async Task<byte[]> GenerateLicenseFileAsync(LicenseGenerationRequest request)
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (!File.Exists(request.ProjectFilePath))
                    {
                        throw new FileNotFoundException($"项目文件不存在: {request.ProjectFilePath}");
                    }

                    var assembly = Assembly.LoadFrom("libs\\LicenseGen.dll");
                    var licenseGenType = assembly.GetType("LicenseGenerator");
                    
                    if (licenseGenType == null)
                    {
                        throw new TypeLoadException("未找到 LicenseGenerator 类型");
                    }

                    var licenseGen = Activator.CreateInstance(licenseGenType, new object[] { request.ProjectFilePath });

                    // 尝试设置属性（使用反射）
                    var properties = licenseGenType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                    
                    foreach (var prop in properties)
                    {
                        try
                        {
                            switch (prop.Name)
                            {
                                case "HardwareLock_Enabled":
                                    prop.SetValue(licenseGen, request.HardwareLockEnabled);
                                    break;
                                case "HardwareID":
                                    if (!string.IsNullOrEmpty(request.HardwareId))
                                        prop.SetValue(licenseGen, request.HardwareId);
                                    break;
                                case "ExpirationDateLock_Enabled":
                                    prop.SetValue(licenseGen, request.ExpirationLockEnabled);
                                    break;
                                case "ExpirationDate":
                                    if (request.ExpirationDate.HasValue)
                                        prop.SetValue(licenseGen, request.ExpirationDate.Value);
                                    break;
                                case "EvaluationLock_Enabled":
                                    prop.SetValue(licenseGen, request.EvaluationLockEnabled);
                                    break;
                                case "EvaluationTime":
                                    if (request.EvaluationTime.HasValue)
                                        prop.SetValue(licenseGen, request.EvaluationTime.Value);
                                    break;
                                case "NumberOfUsesLock_Enabled":
                                    prop.SetValue(licenseGen, request.NumberOfUsesLockEnabled);
                                    break;
                                case "NumberOfUses":
                                    if (request.MaxUses.HasValue)
                                        prop.SetValue(licenseGen, request.MaxUses.Value);
                                    break;
                                case "NumberOfInstancesLock_Enabled":
                                    prop.SetValue(licenseGen, request.NumberOfInstancesLockEnabled);
                                    break;
                                case "NumberOfInstances":
                                    if (request.MaxInstances.HasValue)
                                        prop.SetValue(licenseGen, request.MaxInstances.Value);
                                    break;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"设置属性 {prop.Name} 失败: {ex.Message}");
                        }
                    }

                    // 尝试设置附加信息
                    var addInfoProp = licenseGenType.GetProperty("AdditonalLicenseInformation");
                    if (addInfoProp != null && request.AdditionalInfo != null && request.AdditionalInfo.Count > 0)
                    {
                        try
                        {
                            var addInfo = addInfoProp.GetValue(licenseGen);
                            var addMethod = addInfo?.GetType().GetMethod("Add");
                            
                            if (addMethod != null)
                            {
                                foreach (var info in request.AdditionalInfo)
                                {
                                    addMethod.Invoke(addInfo, new object[] { info.Key, info.Value });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"设置附加信息失败: {ex.Message}");
                        }
                    }

                    // 生成授权文件到字节数组
                    _logger.LogInformation($"开始生成授权文件，项目: {Path.GetFileName(request.ProjectFilePath)}");
                    
                    // 生成临时文件
                    string tempFilePath = Path.GetTempFileName() + ".license";
                    
                    // 获取所有 CreateLicenseFile 方法信息用于调试
                    var allCreateMethods = licenseGenType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                        .Where(m => m.Name == "CreateLicenseFile")
                        .ToList();
                    
                    _logger.LogInformation($"找到 {allCreateMethods.Count} 个 CreateLicenseFile 方法:");
                    foreach (var method in allCreateMethods)
                    {
                        _logger.LogInformation($"  - {method.ReturnType.Name} {method.Name}({string.Join(", ", method.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"))})");
                    }
                    
                    // 尝试获取指定参数类型的方法
                    var createMethod = licenseGenType.GetMethod("CreateLicenseFile", new Type[] { typeof(string) });
                    if (createMethod != null)
                    {
                        _logger.LogInformation($"使用方法: {createMethod.ReturnType.Name} {createMethod.Name}({string.Join(", ", createMethod.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"))})");
                        createMethod.Invoke(licenseGen, new object[] { tempFilePath });
                    }
                    else
                    {
                        throw new MissingMethodException($"未找到 CreateLicenseFile(string) 方法。可用方法: {string.Join(", ", allCreateMethods.Select(m => $"{m.ReturnType.Name} {m.Name}({string.Join(", ", m.GetParameters().Select(p => p.ParameterType.Name))})"))}");
                    }
                    
                    // 读取文件内容
                    byte[] licenseBytes = File.ReadAllBytes(tempFilePath);
                    
                    // 删除临时文件
                    File.Delete(tempFilePath);
                    
                    _logger.LogInformation($"授权文件生成成功，大小: {licenseBytes.Length} 字节");
                    
                    return licenseBytes;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"生成授权文件失败: {ex.Message}");
                    throw;
                }
            });
        }

        public async Task<string> GenerateLicenseFileToDiskAsync(LicenseGenerationRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.OutputFileName))
                {
                    // 生成默认文件名
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    request.OutputFileName = $"InduVision_License_{timestamp}.license";
                }

                // 确保输出目录存在
                string? outputDir = Path.GetDirectoryName(request.OutputFileName);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                byte[] licenseBytes = await GenerateLicenseFileAsync(request);
                
                // 写入文件
                await File.WriteAllBytesAsync(request.OutputFileName, licenseBytes);
                
                _logger.LogInformation($"授权文件已保存到: {request.OutputFileName}");
                
                return request.OutputFileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存授权文件到磁盘失败: {ex.Message}");
                throw;
            }
        }
    }
}