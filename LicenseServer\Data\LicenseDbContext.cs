using LicenseServer.Models;
using Microsoft.EntityFrameworkCore;

namespace LicenseServer.Data
{
    public class LicenseDbContext : DbContext
    {
        public LicenseDbContext(DbContextOptions<LicenseDbContext> options) : base(options)
        {
        }

        public DbSet<LicenseStatusReport> LicenseReports { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 明确指定表名为小写，避免大小写问题
            modelBuilder.Entity<LicenseStatusReport>()
                .ToTable("licensereports");

            // 配置索引
            modelBuilder.Entity<LicenseStatusReport>()
                .HasIndex(l => l.MachineId);

            modelBuilder.Entity<LicenseStatusReport>()
                .HasIndex(l => l.ReportTime);
        }
    }
} 