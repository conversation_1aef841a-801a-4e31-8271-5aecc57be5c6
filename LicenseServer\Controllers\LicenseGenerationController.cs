using Microsoft.AspNetCore.Mvc;
using LicenseServer.Services;
using Microsoft.AspNetCore.Authorization;

namespace LicenseServer.Controllers
{
    [ApiController]
    [Route("api/license-gen")]
    [Authorize]
    public class LicenseGenerationController : ControllerBase
    {
        private readonly ILicenseGenerationService _licenseGenService;
        private readonly ILogger<LicenseGenerationController> _logger;
        private readonly IConfiguration _configuration;

        public LicenseGenerationController(
            ILicenseGenerationService licenseGenService,
            ILogger<LicenseGenerationController> logger,
            IConfiguration configuration)
        {
            _licenseGenService = licenseGenService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 测试项目文件是否可用
        /// </summary>
        [HttpPost("test-project")]
        public async Task<IActionResult> TestProjectFile([FromBody] TestProjectRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ProjectFilePath))
                {
                    return BadRequest(new { success = false, message = "项目文件路径不能为空" });
                }

                bool result = await Task.Run(() => _licenseGenService.TestProjectFile(request.ProjectFilePath));
                
                return Ok(new { 
                    success = result, 
                    message = result ? "项目文件测试成功" : "项目文件测试失败",
                    projectFilePath = request.ProjectFilePath
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"测试项目文件时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 生成授权文件并返回下载
        /// </summary>
        [HttpPost("generate")]
        public async Task<IActionResult> GenerateLicense([FromBody] LicenseGenerationRequest request)
        {
            try
            {
                // 记录前端发送的完整请求
                _logger.LogInformation("收到授权生成请求:");
                _logger.LogInformation($"  项目文件: {request.ProjectFilePath}");
                _logger.LogInformation($"  硬件锁启用: {request.HardwareLockEnabled}");
                _logger.LogInformation($"  硬件ID: '{request.HardwareId}'");
                _logger.LogInformation($"  过期锁启用: {request.ExpirationLockEnabled}");
                _logger.LogInformation($"  过期日期: {request.ExpirationDate}");
                _logger.LogInformation($"  试用期启用: {request.EvaluationLockEnabled}");
                _logger.LogInformation($"  试用时间: {request.EvaluationTime}");
                _logger.LogInformation($"  使用次数锁启用: {request.NumberOfUsesLockEnabled}");
                _logger.LogInformation($"  最大使用次数: {request.MaxUses}");
                _logger.LogInformation($"  实例数锁启用: {request.NumberOfInstancesLockEnabled}");
                _logger.LogInformation($"  最大实例数: {request.MaxInstances}");
                _logger.LogInformation($"  输出文件名: '{request.OutputFileName}'");
                _logger.LogInformation($"  附加信息数量: {request.AdditionalInfo?.Count ?? 0}");

                if (string.IsNullOrEmpty(request.ProjectFilePath))
                {
                    return BadRequest(new { success = false, message = "项目文件路径不能为空" });
                }

                // 如果没有指定项目文件，使用默认的
                if (!System.IO.File.Exists(request.ProjectFilePath))
                {
                    var defaultProjectPath = _configuration["LicenseSettings:DefaultProjectPath"] ?? 
                                           Path.Combine(Directory.GetCurrentDirectory(), "Config", "InduVisionV2.nrproj");
                    
                    if (System.IO.File.Exists(defaultProjectPath))
                    {
                        request.ProjectFilePath = defaultProjectPath;
                        _logger.LogInformation($"使用默认项目文件: {defaultProjectPath}");
                    }
                    else
                    {
                        return BadRequest(new { success = false, message = $"项目文件不存在: {request.ProjectFilePath}" });
                    }
                }

                byte[] licenseBytes = await _licenseGenService.GenerateLicenseFileAsync(request);
                
                string fileName = string.IsNullOrEmpty(request.OutputFileName) ? 
                    $"InduVision_License_{DateTime.Now:yyyyMMdd_HHmmss}.license" : 
                    request.OutputFileName;

                return new FileContentResult(licenseBytes, "application/octet-stream")
                {
                    FileDownloadName = fileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成授权文件时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 生成授权文件并保存到服务器
        /// </summary>
        [HttpPost("generate-and-save")]
        public async Task<IActionResult> GenerateAndSaveLicense([FromBody] LicenseGenerationRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ProjectFilePath))
                {
                    return BadRequest(new { success = false, message = "项目文件路径不能为空" });
                }

                // 如果没有指定输出路径，使用默认的licenses目录
                if (string.IsNullOrEmpty(request.OutputFileName))
                {
                    string licensesDir = Path.Combine(Directory.GetCurrentDirectory(), "licenses");
                    if (!Directory.Exists(licensesDir))
                    {
                        Directory.CreateDirectory(licensesDir);
                    }
                    
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    request.OutputFileName = Path.Combine(licensesDir, $"InduVision_License_{timestamp}.license");
                }

                string savedFilePath = await _licenseGenService.GenerateLicenseFileToDiskAsync(request);
                
                return Ok(new { 
                    success = true, 
                    message = "授权文件生成并保存成功",
                    filePath = savedFilePath,
                    fileName = Path.GetFileName(savedFilePath),
                    fileSize = new FileInfo(savedFilePath).Length
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成并保存授权文件时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取可用的项目文件列表
        /// </summary>
        [HttpGet("projects")]
        public IActionResult GetAvailableProjects()
        {
            try
            {
                var configDir = Path.Combine(Directory.GetCurrentDirectory(), "Config");
                var projects = new List<object>();

                if (Directory.Exists(configDir))
                {
                    var projectFiles = Directory.GetFiles(configDir, "*.nrproj");
                    foreach (var file in projectFiles)
                    {
                        projects.Add(new
                        {
                            fileName = Path.GetFileName(file),
                            fullPath = file,
                            fileSize = new FileInfo(file).Length,
                            lastModified = System.IO.File.GetLastWriteTime(file)
                        });
                    }
                }

                return Ok(projects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取项目文件列表时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取已生成的授权文件列表
        /// </summary>
        [HttpGet("generated-licenses")]
        public IActionResult GetGeneratedLicenses()
        {
            try
            {
                var licensesDir = Path.Combine(Directory.GetCurrentDirectory(), "licenses");
                var licenses = new List<object>();

                if (Directory.Exists(licensesDir))
                {
                    var licenseFiles = Directory.GetFiles(licensesDir, "*.license");
                    foreach (var file in licenseFiles)
                    {
                        var fileInfo = new FileInfo(file);
                        licenses.Add(new
                        {
                            fileName = fileInfo.Name,
                            fullPath = file,
                            fileSize = fileInfo.Length,
                            createdTime = fileInfo.CreationTime,
                            lastModified = fileInfo.LastWriteTime
                        });
                    }
                }

                return Ok(licenses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取已生成的授权文件列表时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 检查LicenseGen.dll的类型信息
        /// </summary>
        [HttpGet("inspect-dll")]
        public async Task<IActionResult> InspectLicenseGenDll()
        {
            try
            {
                var typeInfo = await Task.Run(() => _licenseGenService.GetLicenseGeneratorType());

                if (typeInfo != null)
                {
                    return Ok(new {
                        success = true,
                        message = "成功获取LicenseGenerator类型信息",
                        typeName = typeInfo.ToString()
                    });
                }
                else
                {
                    return BadRequest(new { success = false, message = "无法获取LicenseGenerator类型信息" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查LicenseGen.dll类型信息时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取项目文件的默认硬件ID
        /// </summary>
        [HttpGet("project-default-hardware-id")]
        public async Task<IActionResult> GetProjectDefaultHardwareId([FromQuery] string projectFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(projectFilePath))
                {
                    return BadRequest(new { success = false, message = "项目文件路径不能为空" });
                }

                var hardwareId = await Task.Run(() => _licenseGenService.GetProjectFileDefaultHardwareId(projectFilePath));

                return Ok(new {
                    success = true,
                    hardwareId = hardwareId,
                    message = "成功获取项目文件默认硬件ID"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取项目文件默认硬件ID时出错: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"服务器内部错误: {ex.Message}" });
            }
        }
    }

    public class TestProjectRequest
    {
        public string ProjectFilePath { get; set; } = "";
    }
}