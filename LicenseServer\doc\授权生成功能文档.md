# InduVision 授权管理系统 - 授权生成功能文档

## 概述

本文档详细介绍了 InduVision 授权管理系统新增的授权生成功能，包括 API 接口、后台页面操作指南以及各项功能的使用说明。

## 功能特性

### 🔧 核心功能
- **授权文件生成**：基于项目文件(.nrproj)生成.license授权文件
- **多种锁定机制**：硬件锁、过期锁、评估锁、使用次数锁、实例数锁
- **灵活配置**：支持自定义硬件ID、过期时间、评估周期等参数
- **附加信息**：支持添加自定义键值对信息
- **多种输出方式**：直接下载或保存到服务器

### 🛡️ 锁定机制说明

#### 1. 硬件锁 (Hardware Lock)
- **功能**：将授权文件绑定到特定硬件设备
- **用途**：防止授权文件在不同设备间随意复制使用
- **配置**：可指定硬件ID，留空则允许任意硬件

#### 2. 过期锁 (Expiration Lock)
- **功能**：设置授权文件的过期日期
- **用途**：实现时间限制的授权模式
- **配置**：可设置具体过期日期和是否启用过期锁

#### 3. 评估锁 (Evaluation Lock)
- **功能**：提供试用期限或运行时间限制
- **用途**：实现试用版授权
- **配置**：
  - 评估时间：天数或分钟数
  - 评估类型：试用天数(Trial_Days)或运行分钟数(Runtime_Minutes)

#### 4. 使用次数锁 (Number Of Uses Lock)
- **功能**：限制软件的最大使用次数
- **用途**：实现按次付费的授权模式
- **配置**：设置最大使用次数和是否启用使用次数锁

#### 5. 实例数锁 (Number Of Instances Lock)
- **功能**：限制同时运行的软件实例数量
- **用途**：控制并发使用数量
- **配置**：设置最大实例数和是否启用实例数锁

## API 文档

### 基础信息
- **基础路径**：`/api/license-gen`
- **认证方式**：JWT Bearer Token
- **Content-Type**：`application/json`

### 1. 测试项目文件

#### POST /api/license-gen/test-project
测试项目文件是否可用于生成授权文件。

**请求参数：**
```json
{
  "projectFilePath": "D:\\path\\to\\project.nrproj"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "项目文件测试成功",
  "projectFilePath": "D:\\path\\to\\project.nrproj"
}
```

### 2. 生成授权文件（下载）

#### POST /api/license-gen/generate
生成授权文件并直接下载。

**请求参数：**
```json
{
  "projectFilePath": "D:\\path\\to\\project.nrproj",
  "hardwareId": "ABC123-XYZ789",
  "hardwareLockEnabled": true,
  "expirationDate": "2024-12-31T00:00:00",
  "expirationLockEnabled": true,
  "evaluationTime": 30,
  "evaluationType": "Trial_Days",
  "evaluationLockEnabled": true,
  "maxUses": 100,
  "numberOfUsesLockEnabled": false,
  "maxInstances": 1,
  "numberOfInstancesLockEnabled": true,
  "outputFileName": "InduVision_License_2024.license",
  "additionalInfo": {
    "Customer": "ABC Company",
    "Version": "2.0",
    "LicenseType": "Professional"
  }
}
```

**响应：**
- 成功：返回文件下载流
- 失败：返回错误信息JSON

### 3. 生成授权文件（保存到服务器）

#### POST /api/license-gen/generate-and-save
生成授权文件并保存到服务器。

**请求参数**：同上，`outputFileName` 为可选参数

**响应示例：**
```json
{
  "success": true,
  "message": "授权文件生成并保存成功",
  "filePath": "D:\\VSRepos\\workstation2\\LicenseServer\\licenses\\InduVision_License_20240801_123456.license",
  "fileName": "InduVision_License_20240801_123456.license",
  "fileSize": 1024
}
```

### 4. 获取项目文件列表

#### GET /api/license-gen/projects
获取可用的项目文件列表。

**响应示例：**
```json
[
  {
    "fileName": "InduVisionV2.nrproj",
    "fullPath": "D:\\VSRepos\\workstation2\\LicenseServer\\Config\\InduVisionV2.nrproj",
    "fileSize": 2048,
    "lastModified": "2024-08-01T10:30:00"
  }
]
```

### 5. 获取已生成的授权文件列表

#### GET /api/license-gen/generated-licenses
获取服务器上已生成的授权文件列表。

**响应示例：**
```json
[
  {
    "fileName": "InduVision_License_20240801_123456.license",
    "fullPath": "D:\\VSRepos\\workstation2\\LicenseServer\\licenses\\InduVision_License_20240801_123456.license",
    "fileSize": 1024,
    "createdTime": "2024-08-01T12:34:56",
    "lastModified": "2024-08-01T12:34:56"
  }
]
```

### 6. 检查LicenseGen.dll状态

#### GET /api/license-gen/inspect-dll
检查LicenseGen.dll的类型信息和状态。

**响应示例：**
```json
{
  "success": true,
  "message": "成功获取LicenseGenerator类型信息",
  "typeName": "LicenseGenerator"
}
```

## 后台页面操作文档

### 1. 页面导航
- 登录系统后，点击左侧菜单中的 **"授权生成"**
- 或者直接访问 `/index.html#license-gen`

### 2. 主要界面区域

#### 左侧：生成表单区域
- **项目文件**：选择要使用的.nrproj项目文件
- **硬件ID**：输入目标设备的硬件ID（可选）
- **到期日期**：设置授权过期日期（可选）
- **评估时间**：设置试用天数（可选）
- **最大使用次数**：设置使用次数限制（可选）
- **最大实例数**：设置并发实例数限制（可选）
- **输出文件名**：自定义生成的授权文件名（可选）
- **附加信息**：添加自定义键值对信息

#### 右侧：系统状态区域
- **LicenseGen.dll状态**：显示DLL文件状态
- **快速操作**：
  - 测试项目文件
  - 刷新项目文件列表
  - 清空表单
  - 调试API
- **已生成的授权**：显示最近生成的授权文件

### 3. 操作步骤

#### 生成并下载授权文件
1. 选择项目文件（必填）
2. 配置授权参数（根据需要）
3. 点击 **"生成并下载"** 按钮
4. 系统生成授权文件并自动下载

#### 生成并保存到服务器
1. 选择项目文件（必填）
2. 配置授权参数（根据需要）
3. 点击 **"生成并保存到服务器"** 按钮
4. 系统生成授权文件并保存到服务器的licenses目录

#### 测试项目文件
1. 选择项目文件
2. 点击右侧的 **"测试项目文件"** 按钮
3. 查看测试结果

### 4. 表单字段详细说明

#### 基础设置
- **项目文件**：选择.nrproj格式的项目文件，这是生成授权的基础
- **硬件ID**：目标设备的唯一标识，启用硬件锁时需要
- **启用硬件锁**：勾选后将授权绑定到特定硬件

#### 时间限制
- **到期日期**：授权失效的具体日期
- **快捷设置**：提供常用时间快速设置按钮
  - **3个月/6个月**：快速设置短期授权
  - **1年/2年**：快速设置标准授权期限
  - **100年**：设置超长期授权（接近永久）
  - **永久**：设置为2099年12月31日，并自动禁用过期锁
- **启用过期锁**：勾选后启用时间限制（使用快捷设置时自动启用）
- **评估时间**：试用期限的天数
- **启用评估锁**：勾选后启用试用限制

#### 使用限制
- **最大使用次数**：软件可使用的总次数
- **启用使用次数锁**：勾选后启用次数限制
- **最大实例数**：可同时运行的软件实例数
- **启用实例数锁**：勾选后启用实例数限制

#### 其他设置
- **输出文件名**：自定义生成的授权文件名，留空使用默认命名
- **附加信息**：可添加多个自定义键值对，如客户信息、版本号等

## 测试项目功能说明

### 1. 功能概述
右侧的 **"测试项目文件"** 按钮用于验证选定的项目文件是否可用于生成授权文件。

### 2. 测试内容
- **文件存在性检查**：确认项目文件存在且可访问
- **文件格式验证**：验证文件格式是否符合.nrproj标准
- **LicenseGen.dll兼容性**：检查项目文件是否与当前的LicenseGen.dll兼容
- **创建实例测试**：尝试创建LicenseGenerator实例以验证项目文件完整性

### 3. 使用场景
- **项目文件验证**：在使用新项目文件前进行验证
- **故障排除**：当授权生成失败时，用于诊断问题
- **环境检查**：确认系统环境配置正确

### 4. 测试结果说明
- **成功**：项目文件可用，可以正常生成授权文件
- **失败**：项目文件存在问题，需要检查文件路径、格式或完整性

## 错误处理

### 常见错误及解决方案

#### 1. "项目文件不存在"
- **原因**：指定的项目文件路径错误
- **解决**：检查文件路径，确保文件存在

#### 2. "未找到LicenseGenerator类型"
- **原因**：LicenseGen.dll文件缺失或损坏
- **解决**：确保libs目录下存在正确的LicenseGen.dll文件

#### 3. "Ambiguous match found"
- **原因**：LicenseGen.dll中存在多个同名方法
- **解决**：系统已自动处理，如遇问题请联系技术支持

#### 4. "生成授权文件失败"
- **原因**：项目文件配置错误或参数不正确
- **解决**：使用测试项目文件功能检查项目文件完整性

## 系统要求

### 服务器端
- .NET 8.0 运行环境
- LicenseGen.dll 文件
- 可用的项目文件(.nrproj)

### 客户端
- 现代浏览器（Chrome、Firefox、Edge等）
- 网络访问权限

## 安全注意事项

1. **授权文件管理**：妥善保管生成的授权文件，避免泄露
2. **访问控制**：确保只有授权人员可以访问授权生成功能
3. **日志记录**：系统会记录所有授权生成操作，便于审计
4. **文件权限**：确保服务器上的授权文件目录具有适当的访问权限

## 技术支持

如遇到问题，请检查：
1. 系统日志中的详细错误信息
2. LicenseGen.dll文件是否完整
3. 项目文件格式是否正确
4. 网络连接和权限设置

---

*文档版本：1.0*  
*最后更新：2025年8月1日*