using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace LicenseServer.Controllers
{
    [ApiController]
    [Route("api/auth")]
    public class AuthController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IConfiguration configuration, ILogger<AuthController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        [HttpPost("login")]
        public IActionResult Login([FromBody] LoginModel model)
        {
            // 简单的用户验证（实际应用中应使用更安全的方式）
            // 在真实环境中，这里应该查询数据库
            if (IsValidUser(model.Username, model.Password))
            {
                var token = GenerateJwtToken(model.Username);
                return Ok(new { token });
            }

            _logger.LogWarning($"登录失败：用户名 {model.Username} 尝试登录");
            return Unauthorized(new { message = "用户名或密码不正确" });
        }

        private bool IsValidUser(string username, string password)
        {
            // 这里应该是实际的用户验证逻辑
            // 在实际应用中，应该查询数据库并使用密码哈希比较
            return username == "admin" && password == "admin";
        }

        private string GenerateJwtToken(string username)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["Secret"] ?? "InduvisionDefaultSecretKey2024";
            var key = Encoding.ASCII.GetBytes(secretKey);
            
            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.Name, username)
                }),
                Expires = DateTime.UtcNow.AddHours(double.Parse(jwtSettings["ExpiryHours"] ?? "24")),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }

    public class LoginModel
    {
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
    }
} 