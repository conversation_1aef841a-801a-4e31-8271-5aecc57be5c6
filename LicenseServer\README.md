# InduVision 授权状态上报系统

这是一个用于收集和管理软件授权状态的系统，包括客户端授权状态收集模块和服务端管理平台。

## 系统架构

系统包含两个主要部分：

1. **客户端授权状态收集模块**
   - 集成到 InduVision 软件中
   - 收集授权状态信息并自动上报
   - 使用 `License.dll` API 获取授权详情

2. **服务端授权状态管理平台**
   - 接收、存储和管理授权状态信息
   - 提供 Web 管理界面查看所有客户端状态
   - 支持授权到期提醒和状态分析

## 客户端实现

### 关键文件

- `LkControls/Utils/LicenseReporter.cs`：授权状态收集和上报组件
- `Program.cs`：集成授权状态上报服务到应用程序启动流程
- `Properties/Settings.settings`：配置文件，包含服务器 URL 和 API 密钥

### 功能特点

- 自动收集授权状态信息（使用 `License.dll` API）
- 定时上报（默认每24小时一次）
- 应用启动时立即上报一次
- 异常处理机制，保证主程序正常运行
- 单例模式实现，避免重复实例化

### 集成方式

1. 将 `LicenseReporter.cs` 添加到项目中
2. 在应用程序启动时调用 `LicenseReporter.Instance.Start()`
3. 在应用程序退出时调用 `LicenseReporter.Instance.Stop()`

## 服务端实现

### 关键文件和目录

- `LicenseServer/`：服务端项目根目录
  - `Models/LicenseStatusReport.cs`：授权状态报告数据模型
  - `Data/LicenseDbContext.cs`：数据库上下文
  - `Services/LicenseReportService.cs`：授权报告服务
  - `Controllers/LicenseController.cs`：API 控制器
  - `Controllers/AuthController.cs`：身份验证控制器
  - `wwwroot/`：前端文件
    - `index.html`：管理界面 HTML
    - `js/app.js`：前端 JavaScript 代码

### 特性

- RESTful API 设计
- JWT 身份验证
- Entity Framework Core 数据访问
- 响应式前端界面（使用 Bootstrap）
- 支持授权状态的全面查看和分析

### 数据库管理

服务端现在采用智能数据库管理策略，避免每次重启都重建数据库：

1. **条件性数据库初始化**：
   - 只在必要时创建或重置数据库
   - 支持数据库迁移应用
   - 可通过配置文件控制行为

2. **配置选项**：
   在 `appsettings.json` 中，您可以配置以下选项：
   ```json
   "DatabaseOptions": {
     "ResetOnStartup": false,    // 是否在启动时重置数据库
     "ApplyMigrations": true,    // 是否应用迁移
     "EnableDetailedLogging": true  // 是否启用详细日志
   }
   ```

3. **不同环境的推荐设置**：

   | 环境 | ResetOnStartup | ApplyMigrations | UseInMemoryDatabase |
   |------|----------------|-----------------|---------------------|
   | 开发 | 可选 true/false | true            | 可选 true/false     |
   | 测试 | true           | false           | 推荐 true           |
   | 生产 | false          | true            | false               |

4. **首次部署**：
   - 首次部署时，系统会自动创建数据库和表结构
   - 无需手动执行迁移命令

5. **数据持久性**：
   - 在正常操作中，数据库及其数据将被保留
   - 应用程序重启不会影响已存储的数据
   - 授权历史记录将被完整保留

### API 端点

- **授权上报**
  - `POST /api/license/report`：接收授权状态报告

- **授权查询**
  - `GET /api/license/list`：获取所有机器的最新授权状态
  - `GET /api/license/status/{machineId}`：获取特定机器的授权状态
  - `GET /api/license/history/{machineId}`：获取特定机器的授权历史
  - `GET /api/license/expiring`：获取即将过期的授权
  - `GET /api/license/expired`：获取已过期的授权

- **身份验证**
  - `POST /api/auth/login`：管理员登录

## 部署指南

### 客户端部署

1. 将 `LicenseReporter.cs` 添加到项目中
2. 修改 `Program.cs` 添加授权上报服务启动代码
3. 编译部署到客户端机器

### 服务端部署

1. 创建数据库（MySQL）
2. 修改 `appsettings.json` 中的数据库连接字符串和其他配置选项
3. 首次启动时，系统会自动创建必要的表结构
4. 发布并部署到 Web 服务器：
   ```
   dotnet publish -c Release
   ```

### 服务端升级部署

当升级服务端时，系统会自动处理数据库更改：

1. 更新代码并部署新版本
2. 确保 `DatabaseOptions.ApplyMigrations` 设置为 `true`
3. 启动应用程序，数据库会自动应用必要的迁移
4. 已有数据将被保留

## 安全性考虑

1. **客户端到服务端通信**
   - 使用 API 密钥验证客户端身份
   - 建议启用 HTTPS 加密传输

2. **管理平台访问**
   - 使用 JWT 令牌身份验证
   - 访问控制和权限管理
   - 密码策略和安全存储

3. **数据备份**
   - 建议定期备份数据库
   - 可以使用数据库管理工具设置自动备份

## 扩展建议

1. **邮件通知**
   - 添加授权即将过期的邮件提醒功能

2. **统计分析**
   - 增加客户端分布统计图表
   - 授权使用趋势分析

3. **多级权限**
   - 实现更细粒度的用户权限管理

4. **高级数据库管理**
   - 添加数据库备份和恢复功能
   - 实现数据清理策略（如删除过旧的报告）

## 技术栈

- **客户端**：C# .NET
- **服务端**：ASP.NET Core 7.0, Entity Framework Core
- **前端**：HTML5, CSS3, JavaScript, Bootstrap 5
- **数据库**：MySQL
- **认证**：JWT (JSON Web Tokens) 