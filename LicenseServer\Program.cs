using LicenseServer.Data;
using LicenseServer.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 添加配置
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

// 禁用HTTPS重定向和证书检查
builder.WebHost.UseKestrel(options => {
    options.ListenAnyIP(3748); // 只监听HTTP端口
});

// 添加数据库上下文
// 根据配置使用MySQL或内存数据库
builder.Services.AddDbContext<LicenseDbContext>(options =>
{
    // 检查是否运行在测试环境或配置为使用内存数据库
    if (builder.Environment.IsDevelopment() && builder.Configuration.GetValue<bool>("UseInMemoryDatabase", false))
    {
        options.UseInMemoryDatabase("InduvisionLicenses");
        Console.WriteLine("使用内存数据库运行...");
    }
    else
    {
        // 使用MySQL数据库
        var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
        var serverVersion = new MySqlServerVersion(new Version(8, 0, 0)); // 替换为您的MySQL版本
        
        options.UseMySql(connectionString, serverVersion, 
            mySqlOptions => mySqlOptions.EnableRetryOnFailure(
                maxRetryCount: 10, 
                maxRetryDelay: TimeSpan.FromSeconds(30), 
                errorNumbersToAdd: null));
        
        Console.WriteLine("使用MySQL数据库运行...");
    }
});

// 添加授权报告服务
builder.Services.AddScoped<LicenseReportService>();

// 添加授权生成服务
builder.Services.AddScoped<ILicenseGenerationService, LicenseGenerationService>();

// 添加控制器
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        // 设置日期时间格式
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
    });

// 添加健康检查
builder.Services.AddHealthChecks();

// 添加认证
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["Secret"] ?? "InduvisionDefaultSecretKey2024");

builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(x =>
{
    x.RequireHttpsMetadata = false; // 不需要HTTPS
    x.SaveToken = true;
    x.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false
    };
});

// 添加Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "InduVision License API", Version = "v1" });
    
    // 添加JWT认证支持
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// 添加CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

var app = builder.Build();

// 启用中间件
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    // 禁用HSTS
    // app.UseHsts();
}

// 禁用HTTPS重定向
// app.UseHttpsRedirection();

// 静态文件中间件配置
app.UseDefaultFiles(); // 支持默认文档如index.html
app.UseStaticFiles();  // 提供wwwroot下的静态文件

app.UseRouting();
app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

// 添加健康检查端点
app.MapHealthChecks("/health");

// 添加根路径重定向
app.MapGet("/", () => Results.Redirect("/index.html"));

// 添加简单的API路径描述
app.MapGet("/api", () => new {
    status = "running",
    message = "InduVision License API 服务正在运行",
    endpoints = new[] {
        new { path = "/api/auth/login", method = "POST", description = "用户登录" },
        new { path = "/api/license/report", method = "POST", description = "上报授权状态" },
        new { path = "/api/license/list", method = "GET", description = "获取所有授权状态" },
        new { path = "/api/license/history/{machineId}", method = "GET", description = "获取授权历史" },
        new { path = "/api/license/status/{machineId}", method = "GET", description = "获取授权状态" },
        new { path = "/api/license/expiring", method = "GET", description = "获取即将过期的授权" },
        new { path = "/api/license/expired", method = "GET", description = "获取已过期的授权" }
    },
    version = "1.0",
    time = DateTime.Now
});

app.MapControllers();

// 确保数据库已创建
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<LicenseDbContext>();
        var logger = services.GetRequiredService<ILogger<Program>>();
        var configuration = services.GetRequiredService<IConfiguration>();
        
        // 获取数据库初始化配置
        bool resetDatabase = configuration.GetValue<bool>("DatabaseOptions:ResetOnStartup", false);
        bool useInMemoryDb = configuration.GetValue<bool>("UseInMemoryDatabase", false);
        bool applyMigrations = configuration.GetValue<bool>("DatabaseOptions:ApplyMigrations", true);
        
        if (useInMemoryDb)
        {
            // 内存数据库总是需要重新创建
            logger.LogInformation("使用内存数据库，将创建新的数据库实例");
            context.Database.EnsureCreated();
        }
        else
        {
            // 检查是否可以连接到数据库
            bool canConnect = false;
            try
            {
                canConnect = context.Database.CanConnect();
                logger.LogInformation("数据库连接状态: {status}", canConnect ? "可连接" : "不可连接");
            }
            catch (Exception ex)
            {
                logger.LogWarning("检查数据库连接时出错: {error}", ex.Message);
            }
            
            // 根据配置和环境决定是否重置数据库
            if (resetDatabase)
            {
                logger.LogWarning("根据配置，将重置数据库...");
                try
                {
                    bool deleted = context.Database.EnsureDeleted();
                    logger.LogInformation("数据库删除结果: {result}", deleted ? "已删除" : "未删除");
                    
                    // 重新创建数据库
                    bool created = context.Database.EnsureCreated();
                    logger.LogInformation("数据库创建结果: {result}", created ? "已创建" : "未创建");
                    
                    // 初始化种子数据
                    SeedDatabaseData(context, logger);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "重置数据库时出错");
                }
            }
            else if (!canConnect)
            {
                // 数据库不存在，创建新数据库
                logger.LogInformation("数据库不存在，将创建新数据库...");
                try
                {
                    bool created = context.Database.EnsureCreated();
                    logger.LogInformation("数据库创建结果: {result}", created ? "已创建" : "未创建");
                    
                    // 初始化种子数据
                    SeedDatabaseData(context, logger);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "创建数据库时出错");
                }
            }
            else if (applyMigrations)
            {
                // 数据库存在且需要应用迁移
                logger.LogInformation("数据库已存在，应用迁移...");
                try
                {
                    context.Database.Migrate();
                    logger.LogInformation("数据库迁移已应用");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "应用数据库迁移时出错");
                }
            }
            else
            {
                logger.LogInformation("数据库已存在，无需任何操作");
            }
        }
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "初始化数据库时出错");
        Console.WriteLine($"数据库初始化错误: {ex.Message}");
        if (ex.InnerException != null)
        {
            Console.WriteLine($"内部错误: {ex.InnerException.Message}");
        }
    }
}

// 种子数据初始化方法
void SeedDatabaseData(LicenseDbContext context, ILogger logger)
{
    try
    {
        logger.LogInformation("开始初始化种子数据...");
        
        // 在这里添加初始数据
        // 例如:
        // - 添加管理员用户
        // - 添加初始配置数据
        // - 添加示例数据（可选）
        
        logger.LogInformation("种子数据初始化完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "初始化种子数据时出错");
    }
}

Console.WriteLine("授权服务器启动成功，可通过以下地址访问：");
Console.WriteLine($"Web管理界面: http://localhost:3748");
Console.WriteLine($"API文档: http://localhost:3748/swagger");
Console.WriteLine($"健康检查: http://localhost:3748/health");
Console.WriteLine($"API说明: http://localhost:3748/api");

app.Run(); 