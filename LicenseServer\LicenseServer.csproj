<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseIISIntegration>false</UseIISIntegration>
    <UseIIS>false</UseIIS>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetRootPath>wwwroot</StaticWebAssetRootPath>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="LicenseGen">
      <HintPath>libs\LicenseGen.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Remove="LicenseGen.dll" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Reflection.TypeExtensions" Version="4.7.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\css\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Config\InduVisionV2.nrproj" />
  </ItemGroup>

</Project> 